import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/converters/data_type_ui_mapping.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:nsl/providers/selected_object_provider.dart';
import 'package:nsl/providers/accordion_availability_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/screens/web/static_flow/my_library_service.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:provider/provider.dart';
import 'package:nsl/providers/creation_provider.dart';

/// Data model classes for form data storage

/// Model for Nested Function Configuration form data
class NestedFunctionFormData {
  String attributeName;
  String functionType;
  String functionName;
  String functionInputs;
  String condition;

  NestedFunctionFormData({
    this.attributeName = '',
    this.functionType = '',
    this.functionName = '',
    this.functionInputs = '',
    this.condition = '',
  });

  /// Create a copy of this object with updated values
  NestedFunctionFormData copyWith({
    String? attributeName,
    String? functionType,
    String? functionName,
    String? functionInputs,
    String? condition,
  }) {
    return NestedFunctionFormData(
      attributeName: attributeName ?? this.attributeName,
      functionType: functionType ?? this.functionType,
      functionName: functionName ?? this.functionName,
      functionInputs: functionInputs ?? this.functionInputs,
      condition: condition ?? this.condition,
    );
  }

  /// Check if the form data is valid
  bool isValid() {
    return functionType.isNotEmpty;
  }

  @override
  String toString() {
    return 'NestedFunctionFormData(attributeName: $attributeName, functionType: $functionType, functionName: $functionName, functionInputs: $functionInputs, condition: $condition)';
  }
}

/// Model for Data Mapping form data
class DataMappingFormData {
  String loName;
  String sourceLO1;
  String sourceLO2;
  // Separated entity and attribute fields
  String sourceEntity1;
  String sourceAttribute1;
  String sourceEntity2;
  String sourceAttribute2;
  // Target context fields
  String targetLO;
  String targetEntity;
  String targetAttribute;
  // Legacy combined fields (deprecated but kept for backward compatibility)
  String sourceEntityAttribute1;
  String sourceEntityAttribute2;

  DataMappingFormData({
    this.loName = '',
    this.sourceLO1 = '',
    this.sourceLO2 = '',
    // Separated entity and attribute fields
    this.sourceEntity1 = '',
    this.sourceAttribute1 = '',
    this.sourceEntity2 = '',
    this.sourceAttribute2 = '',
    // Target context fields
    this.targetLO = '',
    this.targetEntity = '',
    this.targetAttribute = '',
    // Legacy combined fields
    this.sourceEntityAttribute1 = '',
    this.sourceEntityAttribute2 = '',
  });

  /// Create a copy of this object with updated values
  DataMappingFormData copyWith({
    String? loName,
    String? sourceLO1,
    String? sourceLO2,
    // Separated entity and attribute parameters
    String? sourceEntity1,
    String? sourceAttribute1,
    String? sourceEntity2,
    String? sourceAttribute2,
    // Target context parameters
    String? targetLO,
    String? targetEntity,
    String? targetAttribute,
    // Legacy combined parameters
    String? sourceEntityAttribute1,
    String? sourceEntityAttribute2,
  }) {
    return DataMappingFormData(
      loName: loName ?? this.loName,
      sourceLO1: sourceLO1 ?? this.sourceLO1,
      sourceLO2: sourceLO2 ?? this.sourceLO2,
      // Separated entity and attribute fields
      sourceEntity1: sourceEntity1 ?? this.sourceEntity1,
      sourceAttribute1: sourceAttribute1 ?? this.sourceAttribute1,
      sourceEntity2: sourceEntity2 ?? this.sourceEntity2,
      sourceAttribute2: sourceAttribute2 ?? this.sourceAttribute2,
      // Target context fields
      targetLO: targetLO ?? this.targetLO,
      targetEntity: targetEntity ?? this.targetEntity,
      targetAttribute: targetAttribute ?? this.targetAttribute,
      // Legacy combined fields
      sourceEntityAttribute1:
          sourceEntityAttribute1 ?? this.sourceEntityAttribute1,
      sourceEntityAttribute2:
          sourceEntityAttribute2 ?? this.sourceEntityAttribute2,
    );
  }

  /// Check if the form data is valid
  bool isValid() {
    return true; // Always valid for demo purposes
  }

  @override
  String toString() {
    return 'DataMappingFormData(loName: $loName, sourceLO1: $sourceLO1, sourceLO2: $sourceLO2, sourceEntity1: $sourceEntity1, sourceAttribute1: $sourceAttribute1, sourceEntity2: $sourceEntity2, sourceAttribute2: $sourceAttribute2, targetLO: $targetLO, targetEntity: $targetEntity, targetAttribute: $targetAttribute, sourceEntityAttribute1: $sourceEntityAttribute1, sourceEntityAttribute2: $sourceEntityAttribute2)';
  }
}

/// Model for Select Entity Attribute form data
class SelectEntityAttributeFormData {
  String sourceEntityAttribute;
  String condition;

  SelectEntityAttributeFormData({
    this.sourceEntityAttribute = '',
    this.condition = 'Create',
  });

  /// Create a copy of this object with updated values
  SelectEntityAttributeFormData copyWith({
    String? sourceEntityAttribute,
    String? condition,
  }) {
    return SelectEntityAttributeFormData(
      sourceEntityAttribute:
          sourceEntityAttribute ?? this.sourceEntityAttribute,
      condition: condition ?? this.condition,
    );
  }

  /// Check if the form data is valid
  bool isValid() {
    return sourceEntityAttribute.isNotEmpty &&
        sourceEntityAttribute != 'Select';
  }

  @override
  String toString() {
    return 'SelectEntityAttributeFormData(sourceEntityAttribute: $sourceEntityAttribute, condition: $condition)';
  }
}

/// Model for Condition Potentiality form data
class ConditionPotentialityFormData {
  String sourceEntity;
  String sourceEntityAttribute;

  ConditionPotentialityFormData({
    this.sourceEntity = '',
    this.sourceEntityAttribute = '',
  });

  /// Create a copy of this object with updated values
  ConditionPotentialityFormData copyWith({
    String? sourceEntity,
    String? sourceEntityAttribute,
  }) {
    return ConditionPotentialityFormData(
      sourceEntity: sourceEntity ?? this.sourceEntity,
      sourceEntityAttribute:
          sourceEntityAttribute ?? this.sourceEntityAttribute,
    );
  }

  /// Check if the form data is valid
  bool isValid() {
    return sourceEntity.isNotEmpty &&
        sourceEntity != 'Select' &&
        sourceEntityAttribute.isNotEmpty &&
        sourceEntityAttribute != 'Select Entity.Attribute';
  }

  @override
  String toString() {
    return 'ConditionPotentialityFormData(sourceEntity: $sourceEntity, sourceEntityAttribute: $sourceEntityAttribute)';
  }
}

/// Local Objective Input Stack Accordion Component
/// Displays an accordion-style interface for managing input stack attributes
class LoInputStackAccordion extends StatefulWidget {
  final AccordionController? accordionController;
  final String title;
  final int attributeCount;
  final List<String> availableAttributes;
  final Function(String)? onAttributeSelected;
  final VoidCallback? onMyLibraryPressed;
  final bool isExpanded;
  final Function(bool)? onExpansionChanged;

  // LO-specific data (replaces global provider dependency)
  final int? loIndex; // LO index for state management
  final Map<String, dynamic>? selectedObject;
  final List<String>? selectedObjectAttributes;
  final List<SelectedObjectData>? selectedObjects;

  // LO-specific callbacks
  final Function(String)? onRemoveObject; // Callback to remove an object
  final Function(int, Map<String, dynamic>, List<String>)?
      onSetSelectedObject; // LO-specific object setter
  final Function(int, Map<String, dynamic>, List<ObjectAttribute>)?
      onAddSelectedObject; // LO-specific object adder
  final Function(int, String)?
      onRemoveSelectedObject; // LO-specific object remover
  final Function(int)? onClearSelectedObjects; // LO-specific clear callback

  const LoInputStackAccordion({
    super.key,
    this.accordionController,
    this.title = 'Inputs Stack',
    this.attributeCount = 0,
    this.availableAttributes = const [],
    this.onAttributeSelected,
    this.onMyLibraryPressed,
    this.isExpanded = false,
    this.onExpansionChanged,
    this.loIndex,
    this.selectedObject,
    this.selectedObjectAttributes,
    this.selectedObjects,
    this.onRemoveObject,
    this.onSetSelectedObject,
    this.onAddSelectedObject,
    this.onRemoveSelectedObject,
    this.onClearSelectedObjects,
  });

  @override
  State<LoInputStackAccordion> createState() => _LoInputStackAccordionState();
}

class _LoInputStackAccordionState extends State<LoInputStackAccordion> {
  late AccordionController _internalController;
  bool _isExpanded = false;
  bool _isCPToggleEnabled = false; // Add toggle state

  // Multiple objects data - Map of object ID to its table data
  late Map<String, List<ObjectAttribute>> multipleObjectsData;

  // Static state persistence for Data Source and Function Type selections
  static Map<String, Map<String, String>> _persistedMultipleObjectDataSources =
      {};
  static Map<String, Map<String, String>>
      _persistedMultipleObjectFunctionTypes = {};

  // Controllers for editable fields - multiple objects
  Map<String, List<TextEditingController>> multipleHelperTextControllers = {};
  Map<String, List<TextEditingController>> multipleErrorMessageControllers = {};

  @override
  void initState() {
    super.initState();
    _internalController = widget.accordionController ?? AccordionController();
    _isExpanded = widget.isExpanded;

    // Initialize data structures
    multipleObjectsData = {};
    _initializeMultipleObjectsData();

    // Report accordion availability after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _reportAccordionAvailability();
    });
  }

  @override
  void dispose() {
    // Dispose all controllers
    _disposeMultipleObjectControllers();

    // Report accordion unavailability when disposing
    _reportAccordionUnavailability();
    super.dispose();
  }

  /// Clear persisted state for specific object attributes (useful when objects change)
  static void clearPersistedStateForObject(String objectId) {
    _persistedMultipleObjectDataSources.remove(objectId);
    _persistedMultipleObjectFunctionTypes.remove(objectId);
    // Modal form data is now stored directly in ObjectAttribute fields
  }

  /// Clear all persisted state (useful for complete reset)
  static void clearAllPersistedState() {
    _persistedMultipleObjectDataSources.clear();
    _persistedMultipleObjectFunctionTypes.clear();
    // Modal form data is now stored directly in ObjectAttribute fields
  }

  /// Report accordion availability to the provider
  void _reportAccordionAvailability() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.markAccordionAvailable(
        isExpanded: _isExpanded,
        screenContext: 'LoInputStackAccordion',
      );
    } catch (e) {
      // Provider might not be available in some contexts (like demo screens)
      // This is acceptable - just continue without reporting
    }
  }

  /// Report accordion unavailability to the provider
  void _reportAccordionUnavailability() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.markAccordionUnavailable();
    } catch (e) {
      // Provider might not be available in some contexts
      // This is acceptable - just continue without reporting
    }
  }

  void _initializeMultipleObjectsData() {
    Logger.info('🔄 _initializeMultipleObjectsData called');
    Logger.info(
        '📊 Current selectedObjects count: ${widget.selectedObjects?.length ?? 0}');

    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      // Load previously saved data first
      _loadEntityTableData();

      Logger.info('📋 Processing ${widget.selectedObjects!.length} objects');

      for (final objectData in widget.selectedObjects!) {
        Logger.info(
            '🔍 Processing object: ${objectData.id} with ${objectData.attributes.length} attributes');

        // Check if this object needs initialization or updating
        final existingData = multipleObjectsData[objectData.id];
        final needsInitialization = existingData == null ||
            existingData.length != objectData.attributes.length;

        if (needsInitialization) {
          Logger.info(
              '✨ Initializing/updating data for object: ${objectData.id}');
          Logger.info('   - Existing data: ${existingData?.length ?? 0} rows');
          Logger.info(
              '   - Required attributes: ${objectData.attributes.length}');

          if (existingData == null) {
            // Create new data for completely new object
            multipleObjectsData[objectData.id] =
                objectData.attributes.map((attribute) {
              final attrName = attribute.displayName ?? attribute.name ?? '';
              final persistedDataSource =
                  _persistedMultipleObjectDataSources[objectData.id] != null
                      ? _persistedMultipleObjectDataSources[objectData.id]![
                              attrName] ??
                          ''
                      : '';
              final persistedFunctionType =
                  _persistedMultipleObjectFunctionTypes[objectData.id] != null
                      ? _persistedMultipleObjectFunctionTypes[objectData.id]![
                              attrName] ??
                          ''
                      : '';
              return ObjectAttribute(
                name: attribute.name,
                displayName: attribute.displayName,
                dataSource: persistedDataSource,
                functionType: persistedFunctionType,
                status: (attribute.required ?? false) ? 'required' : 'optional',
                uiControl: attribute.uiControl,
                helperText: attribute.helperText,
                errorMessage: attribute.errorMessage,
                dataType: attribute.dataType,
                id: attribute.id,
                required: attribute.required,
                unique: attribute.unique,
                defaultType: attribute.defaultType,
                defaultValue: attribute.defaultValue,
                description: attribute.description,
                enumValues: attribute.enumValues,
                validation: attribute.validation,
                isPrimaryKey: attribute.isPrimaryKey,
                isForeignKey: attribute.isPrimaryKey,
              );
            }).toList();
            Logger.info(
                '   - Created ${multipleObjectsData[objectData.id]!.length} table rows');
          } else {
            // Update existing data when new attributes are added
            Logger.info('   - Updating existing data with new attributes');
            final existingAttributes =
                existingData.map((row) => row.displayName ?? '').toSet();
            final newAttributes = objectData.attributes
                .map((attribute) =>
                    attribute.displayName ?? attribute.name ?? '')
                .toSet();

            // Add rows for new attributes that don't exist yet
            for (final attribute in objectData.attributes) {
              final attrName = attribute.displayName ?? attribute.name ?? '';
              if (!existingAttributes.contains(attrName)) {
                Logger.info('   - Adding new attribute: $attrName');
                final persistedDataSource =
                    _persistedMultipleObjectDataSources[objectData.id] != null
                        ? _persistedMultipleObjectDataSources[objectData.id]![
                                attrName] ??
                            ''
                        : '';
                final persistedFunctionType =
                    _persistedMultipleObjectFunctionTypes[objectData.id] != null
                        ? _persistedMultipleObjectFunctionTypes[objectData.id]![
                                attrName] ??
                            ''
                        : '';
                existingData.add(ObjectAttribute(
                  name: attribute.name,
                  displayName: attribute.displayName,
                  dataSource: persistedDataSource,
                  functionType: persistedFunctionType,
                  status:
                      (attribute.required ?? false) ? 'required' : 'optional',
                  uiControl: attribute.uiControl,
                  helperText: attribute.helperText,
                  errorMessage: attribute.errorMessage,
                  dataType: attribute.dataType,
                  id: attribute.id,
                  required: attribute.required,
                  unique: attribute.unique,
                  defaultType: attribute.defaultType,
                  defaultValue: attribute.defaultValue,
                  description: attribute.description,
                  enumValues: attribute.enumValues,
                  validation: attribute.validation,
                  isPrimaryKey: attribute.isPrimaryKey,
                  isForeignKey: attribute.isPrimaryKey,
                ));
              }
            }

            // Remove rows for attributes that no longer exist
            existingData
                .removeWhere((row) => !newAttributes.contains(row.displayName));

            Logger.info('   - Updated to ${existingData.length} table rows');
          }
        } else {
          Logger.info(
              '⏭️ Object ${objectData.id} already has correct data (${existingData.length} rows), skipping');
        }
      }

      // Remove data for objects that are no longer in the selectedObjects list
      final currentObjectIds =
          widget.selectedObjects!.map((obj) => obj.id).toSet();
      final keysToRemove = multipleObjectsData.keys
          .where((key) => !currentObjectIds.contains(key))
          .toList();
      for (final key in keysToRemove) {
        Logger.info('🗑️ Removing data for object: $key');
        multipleObjectsData.remove(key);
      }

      _initializeMultipleObjectControllers();
      Logger.info('✅ _initializeMultipleObjectsData completed');
    } else {
      Logger.info('📭 No selectedObjects, clearing data');
      multipleObjectsData.clear();
    }
  }

  void _initializeMultipleObjectControllers() {
    Logger.info('🎮 _initializeMultipleObjectControllers called');

    // Dispose existing controllers
    _disposeMultipleObjectControllers();

    // Initialize controllers for each object
    if (widget.selectedObjects != null) {
      for (final objectData in widget.selectedObjects!) {
        final objectTableData = multipleObjectsData[objectData.id] ?? [];
        Logger.info(
            '🎮 Initializing controllers for object: ${objectData.id} with ${objectTableData.length} rows');

        if (objectTableData.isNotEmpty) {
          multipleHelperTextControllers[objectData.id] = objectTableData
              .map((item) => TextEditingController(text: ''))
              .toList();

          multipleErrorMessageControllers[objectData.id] = objectTableData
              .map((item) => TextEditingController(text: ''))
              .toList();

          // Add listeners to sync controller changes with data model
          final helperControllers =
              multipleHelperTextControllers[objectData.id]!;
          final errorControllers =
              multipleErrorMessageControllers[objectData.id]!;

          for (int i = 0; i < helperControllers.length; i++) {
            helperControllers[i].addListener(() {
              if (i < objectTableData.length) {
                multipleObjectsData[objectData.id]![i].helperText =
                    helperControllers[i].text;
                // Save data to provider after text change
                _saveEntityTableData();
              }
            });
          }

          for (int i = 0; i < errorControllers.length; i++) {
            errorControllers[i].addListener(() {
              if (i < objectTableData.length) {
                multipleObjectsData[objectData.id]![i].errorMessage =
                    errorControllers[i].text;
                // Save data to provider after text change
                _saveEntityTableData();
              }
            });
          }

          Logger.info('✅ Controllers initialized for object: ${objectData.id}');
        } else {
          Logger.info('⚠️ No table data found for object: ${objectData.id}');
        }
      }
    }

    Logger.info('🎮 _initializeMultipleObjectControllers completed');
  }

  /// Ensure data consistency before building the widget
  void _ensureDataConsistency() {
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      bool needsUpdate = false;

      // Check if any objects are missing data
      for (final objectData in widget.selectedObjects!) {
        if (!multipleObjectsData.containsKey(objectData.id) ||
            multipleObjectsData[objectData.id]!.isEmpty ||
            multipleObjectsData[objectData.id]!.length !=
                objectData.attributes.length) {
          Logger.info(
              '🔍 _ensureDataConsistency: Object ${objectData.id} needs data initialization');
          needsUpdate = true;
          break;
        }
      }

      if (needsUpdate) {
        Logger.info(
            '🔄 _ensureDataConsistency: Triggering data initialization');
        _initializeMultipleObjectsData();
      }
    }
  }

  void _disposeMultipleObjectControllers() {
    for (var controllerList in multipleHelperTextControllers.values) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    multipleHelperTextControllers.clear();

    for (var controllerList in multipleErrorMessageControllers.values) {
      for (var controller in controllerList) {
        controller.dispose();
      }
    }
    multipleErrorMessageControllers.clear();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    widget.onExpansionChanged?.call(_isExpanded);
    // Toggle left panel when collapse is clicked
    // final creationProvider =
    //     Provider.of<CreationProvider>(context, listen: false);
    // creationProvider.toggleLeftPanel();
    if (widget.accordionController != null) {
      widget.accordionController!.togglePanel('lo_input_stack');
    }
    // Report expansion state change
    _reportExpansionChange();
  }

  /// Report expansion state change to the provider
  void _reportExpansionChange() {
    try {
      final accordionProvider = Provider.of<AccordionAvailabilityProvider>(
        context,
        listen: false,
      );
      accordionProvider.updateAccordionExpansion(_isExpanded);
    } catch (e) {
      // Provider might not be available in some contexts
      // This is acceptable - just continue without reporting
    }
  }

  /// Save entity table data directly to GoDetailsProvider's currentGoModel entitiesList for persistence
  void _saveEntityTableData() {
    if (widget.loIndex == null) return;

    try {
      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel == null) {
        Logger.info('❌ Error: GoDetailsProvider currentGoModel is null');
        return;
      }

      // Ensure localObjectivesList exists and has the required LO
      if (goModel.localObjectivesList == null ||
          widget.loIndex! >= goModel.localObjectivesList!.length) {
        Logger.info(
            '❌ Error: GoModel or LO at index ${widget.loIndex} not found');
        Logger.info(
            '   localObjectivesList length: ${goModel.localObjectivesList?.length}');
        return;
      }

      final lo = goModel.localObjectivesList![widget.loIndex!];

      // Initialize entitiesList if null
      lo.entitiesList ??= [];

      Logger.info('💾 Starting save for LO-${widget.loIndex! + 1}');
      Logger.info('   Current entitiesList length: ${lo.entitiesList!.length}');

      // Save multiple objects data
      if (widget.selectedObjects != null &&
          widget.selectedObjects!.isNotEmpty) {
        Logger.info(
            '   Saving ${widget.selectedObjects!.length} multiple objects');
        for (final objectData in widget.selectedObjects!) {
          final entityId = objectData.id;
          final entityName = objectData.object['displayName'] ??
              objectData.object['name'] ??
              'Unknown Entity';

          // Get table data for this entity
          final tableData = multipleObjectsData[entityId] ?? [];
          Logger.info(
              '   Entity $entityId ($entityName): ${tableData.length} rows');

          // Save entity with table configuration data
          _saveEntityToEntitiesList(lo, entityId, entityName, tableData);
        }
      } else {
        Logger.info(
            '   No data to save - selectedObjects: ${widget.selectedObjects?.length}');
      }

      Logger.info(
          '✅ Entity table data saved to GoModel entitiesList for LO-${widget.loIndex! + 1}');
      Logger.info('   Final entitiesList length: ${lo.entitiesList!.length}');
    } catch (e) {
      Logger.info('❌ Error saving entity table data: $e');
      Logger.info('   Stack trace: ${StackTrace.current}');
    }
  }

  /// Save entity with table configuration data to the entitiesList
  void _saveEntityToEntitiesList(LocalObjectivesList lo, String entityId,
      String entityName, List<ObjectAttribute> tableData) {
    try {
      Logger.info('🔧 _saveEntityToEntitiesList: $entityId ($entityName)');
      Logger.info('   Table data rows: ${tableData.length}');

      // Check if entity already exists
      ObjectCreationModel? existingEntity;
      int entityIndex = -1;
      try {
        entityIndex = lo.entitiesList!.indexWhere(
            (entity) => entity.id == entityId || entity.name == entityName);
        if (entityIndex != -1) {
          existingEntity = lo.entitiesList![entityIndex];
          Logger.info(
              '   Found existing entity at index $entityIndex: ${existingEntity.displayName}');
        }
      } catch (e) {
        Logger.info('   Entity not found, will create new one');
      }

      // Merge table data with existing attributes to preserve modal form data
      final List<ObjectAttribute> mergedAttributes =
          _mergeAttributesWithExistingData(
              existingEntity?.attributes, tableData, entityId);

      Logger.info(
          '   Created ${mergedAttributes.length} ObjectAttribute instances with preserved modal form data');

      if (existingEntity != null) {
        // Update existing entity with merged attributes
        final updatedEntity = ObjectCreationModel(
          tenant: existingEntity.tenant,
          entityDeclaration: existingEntity.entityDeclaration,
          id: existingEntity.id,
          name: existingEntity.name,
          displayName: entityName,
          type: existingEntity.type ?? 'entity',
          description: existingEntity.description ??
              'Entity created from LoInputStackAccordion',
          businessPurpose: existingEntity.businessPurpose,
          businessDomain: existingEntity.businessDomain,
          category: existingEntity.category,
          archivalStrategy: existingEntity.archivalStrategy,
          colorTheme: existingEntity.colorTheme,
          icon: existingEntity.icon,
          tags: existingEntity.tags,
          attributes: mergedAttributes,
          relationships: existingEntity.relationships,
          businessRules: existingEntity.businessRules,
          enumValues: existingEntity.enumValues,
          securityClassification: existingEntity.securityClassification,
          systemPermissions: existingEntity.systemPermissions,
          roleSystemPermissions: existingEntity.roleSystemPermissions,
          uiProperties: existingEntity.uiProperties,
          confidence: existingEntity.confidence,
          extractionMethod: existingEntity.extractionMethod,
          completionScore: existingEntity.completionScore,
          configurationStatus: existingEntity.configurationStatus,
          createdAt: existingEntity.createdAt,
          updatedAt: DateTime.now(),
          isEntityValidatedSaved: existingEntity.isEntityValidatedSaved,
          isEntityAttributesValidatedSaved:
              existingEntity.isEntityAttributesValidatedSaved,
          isEntityRelationshipsValidatedSaved:
              existingEntity.isEntityRelationshipsValidatedSaved,
          isEntityBusinessRulesValidatedSaved:
              existingEntity.isEntityBusinessRulesValidatedSaved,
          isEntityEnumValuesValidatedSaved:
              existingEntity.isEntityEnumValuesValidatedSaved,
          isEntitySecurityClassificationValidatedSaved:
              existingEntity.isEntitySecurityClassificationValidatedSaved,
          isEntitySystemPropertiesValidatedSaved:
              existingEntity.isEntitySystemPropertiesValidatedSaved,
          attributesExpanded: existingEntity.attributesExpanded,
          relationshipsExpanded: existingEntity.relationshipsExpanded,
          enumValuesExpanded: existingEntity.enumValuesExpanded,
        );

        // Replace the existing entity
        lo.entitiesList![entityIndex] = updatedEntity;

        Logger.info(
            'Updated existing entity in entitiesList: $entityName with ${mergedAttributes.length} attributes');
      } else {
        // Create new entity
        final newEntity = ObjectCreationModel(
          id: entityId,
          name: entityName,
          displayName: entityName,
          type: 'entity',
          description: 'Entity created from LoInputStackAccordion',
          attributes: mergedAttributes,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        lo.entitiesList!.add(newEntity);
        Logger.info(
            'Added new entity to entitiesList: $entityName with ${mergedAttributes.length} attributes');
      }
    } catch (e) {
      Logger.info('Error saving entity to entitiesList: $e');
    }
  }

  /// Load previously saved entity table data from GoDetailsProvider's currentGoModel entitiesList
  void _loadEntityTableData() {
    if (widget.loIndex == null) return;

    try {
      Logger.info('🔄 _loadEntityTableData for LO-${widget.loIndex! + 1}');

      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel == null) {
        Logger.info('❌ Error: GoDetailsProvider currentGoModel is null');
        return;
      }

      // Ensure localObjectivesList exists and has the required LO
      if (goModel.localObjectivesList == null ||
          widget.loIndex! >= goModel.localObjectivesList!.length) {
        Logger.info(
            '❌ Error: GoModel or LO at index ${widget.loIndex} not found');
        Logger.info(
            '   localObjectivesList length: ${goModel.localObjectivesList?.length}');
        return;
      }

      final lo = goModel.localObjectivesList![widget.loIndex!];
      Logger.info('   LO entitiesList length: ${lo.entitiesList?.length ?? 0}');

      // Get saved entity table data from entitiesList
      final savedTableData = _getEntityTableDataFromEntitiesList(lo);
      Logger.info(
          '   Retrieved saved data for ${savedTableData.keys.length} entities');

      if (savedTableData.isNotEmpty) {
        Logger.info(
            '🔄 Loading saved entity table data for LO-${widget.loIndex! + 1}: ${savedTableData.keys.length} entities');

        // Load multiple objects data
        if (widget.selectedObjects != null &&
            widget.selectedObjects!.isNotEmpty) {
          Logger.info('   Loading for multiple objects mode');
          for (final objectData in widget.selectedObjects!) {
            final entityId = objectData.id;
            if (savedTableData.containsKey(entityId)) {
              multipleObjectsData[entityId] = savedTableData[entityId]!;
              Logger.info(
                  '✅ Loaded table data for entity: $entityId (${savedTableData[entityId]!.length} rows)');
            }
          }
        }
      }
    } catch (e) {
      Logger.info('Error loading entity table data: $e');
    }
  }

  /// Get entity table data from GoModel's entitiesList
  Map<String, List<ObjectAttribute>> _getEntityTableDataFromEntitiesList(
      LocalObjectivesList lo) {
    final Map<String, List<ObjectAttribute>> result = {};

    try {
      if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
        for (final entity in lo.entitiesList!) {
          final entityId = entity.id ?? entity.name ?? 'unknown';
          final List<ObjectAttribute> tableRows = [];

          if (entity.attributes != null && entity.attributes!.isNotEmpty) {
            // Convert ObjectAttribute back to table row format and extract modal form data
            for (final attribute in entity.attributes!) {
              tableRows.add(ObjectAttribute(
                displayName: attribute.displayName ?? attribute.name ?? '',
                dataSource: attribute.dataSource ?? '',
                functionType: attribute.functionType ?? '',
                status: attribute.status ?? 'required',
                uiControl: attribute.uiControl ?? 'String',
                helperText: attribute.helperText ?? '',
                errorMessage: attribute.errorMessage ?? '',
                dataType: attribute.dataType,
                id: attribute.id,
                required: attribute.required,
                unique: attribute.unique,
                defaultType: attribute.defaultType,
                defaultValue: attribute.defaultValue,
                description: attribute.description,
                enumValues: attribute.enumValues,
                validation: attribute.validation,
                isPrimaryKey: attribute.isPrimaryKey,
                isForeignKey: attribute.isPrimaryKey,
              ));

              // Modal form data is now stored directly in ObjectAttribute fields
              Logger.info(
                  '   Loaded attribute with modal form data: ${attribute.displayName}');
            }

            if (tableRows.isNotEmpty) {
              result[entityId] = tableRows;
              Logger.info(
                  'Loaded ${tableRows.length} attributes for entity: ${entity.displayName ?? entity.name}');
            }
          }
        }
      }
    } catch (e) {
      Logger.info('Error getting entity table data from entitiesList: $e');
    }

    return result;
  }

  /// Merge table data with existing attributes to preserve modal form data
  /// Fixed to prevent duplicate attribute instances when dropdown selections are updated
  List<ObjectAttribute> _mergeAttributesWithExistingData(
      List<ObjectAttribute>? existingAttributes,
      List<ObjectAttribute> tableData,
      String entityId) {
    Logger.info('🔄 _mergeAttributesWithExistingData for entity: $entityId');
    Logger.info('   Existing attributes: ${existingAttributes?.length ?? 0}');
    Logger.info('   Table data rows: ${tableData.length}');

    final List<ObjectAttribute> mergedAttributes = [];

    // Create a map of existing attributes by name for quick lookup
    final Map<String, ObjectAttribute> existingAttributeMap = {};
    if (existingAttributes != null) {
      for (final attr in existingAttributes) {
        final key = attr.displayName ?? attr.name ?? '';
        if (key.isNotEmpty) {
          existingAttributeMap[key] = attr;
        }
      }
    }

    // Create a set to track which attributes we've already processed to prevent duplicates
    final Set<String> processedAttributes = {};

    // Process each row in table data
    for (int i = 0; i < tableData.length; i++) {
      final rowData = tableData[i];
      final attributeName = rowData.displayName ?? rowData.name ?? '';

      // Skip if we've already processed this attribute or if name is empty
      if (attributeName.isEmpty ||
          processedAttributes.contains(attributeName)) {
        Logger.info('   Skipping duplicate/empty attribute: $attributeName');
        continue;
      }

      // Mark this attribute as processed
      processedAttributes.add(attributeName);

      Logger.info('   Processing row $i: $attributeName');

      // Check if this attribute already exists
      final existingAttribute = existingAttributeMap[attributeName];

      if (existingAttribute != null) {
        // Merge existing attribute with updated table data, preserving modal form data
        final mergedAttribute = ObjectAttribute(
          // Preserve existing core data
          name: existingAttribute.name,
          displayName: existingAttribute.displayName,
          dataType: existingAttribute.dataType,
          required: existingAttribute.required,
          unique: existingAttribute.unique,
          defaultType: existingAttribute.defaultType,
          defaultValue: existingAttribute.defaultValue,
          description: existingAttribute.description,
          enumValues: existingAttribute.enumValues,
          validation: existingAttribute.validation,
          isPrimaryKey: existingAttribute.isPrimaryKey,
          isForeignKey: existingAttribute.isForeignKey,

          // Update table configuration data from current UI state
          dataSource: rowData.dataSource,
          functionType: rowData.functionType,
          status: (rowData.required ?? false) ? 'required' : 'optional',
          uiControl: rowData.uiControl,
          helperText: rowData.helperText,
          errorMessage: rowData.errorMessage,

          // Preserve all modal form data fields
          nestedFunctionAttributeName:
              existingAttribute.nestedFunctionAttributeName,
          nestedFunctionType: existingAttribute.nestedFunctionType,
          nestedFunctionName: existingAttribute.nestedFunctionName,
          nestedFunctionInputs: existingAttribute.nestedFunctionInputs,
          nestedFunctionCondition: existingAttribute.nestedFunctionCondition,
          dataMappingLoName: existingAttribute.dataMappingLoName,
          dataMappingSourceLO1: existingAttribute.dataMappingSourceLO1,
          dataMappingSourceLO2: existingAttribute.dataMappingSourceLO2,
          dataMappingSourceEntityAttribute1:
              existingAttribute.dataMappingSourceEntityAttribute1,
          dataMappingSourceEntityAttribute2:
              existingAttribute.dataMappingSourceEntityAttribute2,
          selectEntitySourceEntityAttribute:
              existingAttribute.selectEntitySourceEntityAttribute,
          selectEntityCondition: existingAttribute.selectEntityCondition,
          conditionPotentialitySourceEntity:
              existingAttribute.conditionPotentialitySourceEntity,
          conditionPotentialitySourceEntityAttribute:
              existingAttribute.conditionPotentialitySourceEntityAttribute,
        );

        mergedAttributes.add(mergedAttribute);
        Logger.info(
            '     ✅ Merged existing attribute with preserved modal data');
      } else {
        // Create new attribute from table data
        final newAttribute = ObjectAttribute(
          name: rowData.name,
          displayName: attributeName,
          dataSource: rowData.dataSource,
          functionType: rowData.functionType,
          status: (rowData.required ?? false) ? 'required' : 'optional',
          uiControl: rowData.uiControl,
          helperText: rowData.helperText,
          errorMessage: rowData.errorMessage,
          dataType: rowData.dataType, // Default data type
          required: rowData.required,
          id: rowData.id,
          unique: rowData.unique,
          defaultType: rowData.defaultType,
          defaultValue: rowData.defaultValue,
          description: rowData.description,
          enumValues: rowData.enumValues,
          validation: rowData.validation,
          isPrimaryKey: rowData.isPrimaryKey,
          isForeignKey: rowData.isPrimaryKey,
        );

        mergedAttributes.add(newAttribute);
        Logger.info('     ✨ Created new attribute');
      }
    }

    Logger.info('   Final merged attributes: ${mergedAttributes.length}');
    Logger.info(
        '   Processed unique attributes: ${processedAttributes.length}');
    return mergedAttributes;
  }

  /// Convert display names to actual names for data storage
  /// Converts "Furniture Order.Payment Method" to "FurnitureOrder.payment_method"
  String _convertDisplayNameToActualName(
      String displayValue, BuildContext context) {
    try {
      // Skip conversion if the value is empty or a placeholder
      if (displayValue.isEmpty || displayValue == 'Select Entity.Attribute') {
        return displayValue;
      }

      // Try to use MyLibraryService for more accurate conversion
      if (MyLibraryService.isInitializedForDynamicAttributes) {
        final result = _convertUsingLibraryService(displayValue);
        if (result != displayValue) {
          Logger.info('🔄 Library conversion: "$displayValue" to "$result"');
          return result;
        }
      }

      // Fallback to local data conversion
      final result = _convertUsingLocalData(displayValue, context);
      if (result != displayValue) {
        Logger.info('🔄 Local conversion: "$displayValue" to "$result"');
        return result;
      }

      Logger.info('⚠️ No conversion found for "$displayValue", using as-is');
      return displayValue; // Return as-is if no match found
    } catch (e) {
      Logger.info('❌ Error converting display name: $e');
      return displayValue; // Return as-is on error
    }
  }

  /// Convert using MyLibraryService data (more accurate)
  String _convertUsingLibraryService(String displayValue) {
    try {
      // Parse the display value to extract entity and attribute display names
      if (!displayValue.contains('.')) {
        return displayValue; // Not in expected format
      }

      final parts = displayValue.split('.');
      if (parts.length != 2) {
        return displayValue; // Not in expected format
      }

      final entityDisplayName = parts[0];
      final attributeDisplayName = parts[1];

      // We need to find the actual entity and attribute names
      // This is a simplified approach - in a real implementation, we'd need access to the raw API data
      // to map display names back to actual names

      // For now, we'll implement a basic conversion that assumes:
      // - Entity display names like "Furniture Order" become "FurnitureOrder"
      // - Attribute display names like "Payment Method" become "payment_method"

      final actualEntityName =
          _convertEntityDisplayNameToActualName(entityDisplayName);
      final actualAttributeName =
          _convertAttributeDisplayNameToActualName(attributeDisplayName);

      final actualValue = '$actualEntityName.$actualAttributeName';

      // Only return the converted value if it's different from the original
      if (actualValue != displayValue) {
        return actualValue;
      }

      return displayValue; // No conversion needed
    } catch (e) {
      Logger.info('❌ Error in library service conversion: $e');
      return displayValue;
    }
  }

  /// Convert entity display name to actual name
  /// "Furniture Order" -> "FurnitureOrder"
  String _convertEntityDisplayNameToActualName(String displayName) {
    // Remove spaces and convert to PascalCase
    return displayName
        .split(' ')
        .map((word) => word.isNotEmpty
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join('');
  }

  /// Convert attribute display name to actual name
  /// "Payment Method" -> "payment_method"
  String _convertAttributeDisplayNameToActualName(String displayName) {
    // Convert to snake_case
    return displayName.toLowerCase().replaceAll(' ', '_');
  }

  /// Convert using local GO model data (fallback)
  String _convertUsingLocalData(String displayValue, BuildContext context) {
    try {
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);
      final currentGoModel = goDetailsProvider.currentGoModel;

      if (currentGoModel?.localObjectivesList == null ||
          widget.loIndex == null) {
        return displayValue; // Return as-is if no conversion possible
      }

      // Get previous LO indices for DataMapping
      final previousLoIndices = <int>[];
      for (int i = 0; i < widget.loIndex!; i++) {
        previousLoIndices.add(i);
      }

      // Search through previous LOs to find the matching entity and attribute
      for (final loIndex in previousLoIndices) {
        if (loIndex < currentGoModel!.localObjectivesList!.length) {
          final lo = currentGoModel.localObjectivesList![loIndex];

          // Check entitiesList
          if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
            for (final entity in lo.entitiesList!) {
              final entityDisplayName = entity.displayName ?? entity.name ?? '';
              final entityName = entity.name ?? entity.displayName ?? '';

              if (entity.attributes != null && entity.attributes!.isNotEmpty) {
                for (final attribute in entity.attributes!) {
                  final attributeDisplayName =
                      attribute.displayName ?? attribute.name ?? '';
                  final attributeName =
                      attribute.name ?? attribute.displayName ?? '';

                  // Check if this matches the display value
                  final displayOption =
                      '$entityDisplayName.$attributeDisplayName';
                  if (displayOption == displayValue) {
                    final actualOption = '$entityName.$attributeName';
                    return actualOption;
                  }
                }
              }
            }
          }

          // Check selected objects from provider
          final loSelectedObjects =
              goDetailsProvider.getLoSelectedObjectsList(loIndex);
          for (final selectedObjectData in loSelectedObjects) {
            final object = selectedObjectData.object;
            final attributes = selectedObjectData.attributes;

            final entityDisplayName =
                object['displayName'] ?? object['name'] ?? '';
            final entityName = object['name'] ?? object['displayName'] ?? '';

            for (final attributeDisplayName in attributes) {
              final displayOption = '$entityDisplayName.$attributeDisplayName';
              if (displayOption == displayValue) {
                // For selected objects, we need to find the actual attribute name
                // This is a simplified approach - in reality, we'd need to query the API
                final actualOption = '$entityName.$attributeDisplayName';
                return actualOption;
              }
            }
          }
        }
      }

      return displayValue; // No match found
    } catch (e) {
      Logger.info('❌ Error in local data conversion: $e');
      return displayValue;
    }
  }

  /// Save modal form data directly to ObjectAttribute in GoModel
  void saveModalFormDataToAttribute(String entityId, String attributeName,
      Map<String, String> formData, String formType) {
    if (widget.loIndex == null) return;

    try {
      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel?.localObjectivesList == null ||
          widget.loIndex! >= goModel!.localObjectivesList!.length) {
        Logger.info(
            '❌ Error: GoModel or LO not found for modal form data save');
        return;
      }

      final lo = goModel.localObjectivesList![widget.loIndex!];
      if (lo.entitiesList == null) {
        Logger.info('❌ Error: No entities list found for modal form data save');
        return;
      }

      // Find the entity
      final entityIndex = lo.entitiesList!.indexWhere(
          (entity) => entity.id == entityId || entity.name == entityId);

      if (entityIndex == -1) {
        Logger.info(
            '❌ Error: Entity not found for modal form data save: $entityId');
        return;
      }

      final entity = lo.entitiesList![entityIndex];
      if (entity.attributes == null) {
        Logger.info('❌ Error: No attributes found for modal form data save');
        return;
      }

      // Find the attribute
      final attributeIndex = entity.attributes!.indexWhere((attr) =>
          attr.displayName == attributeName || attr.name == attributeName);

      if (attributeIndex == -1) {
        Logger.info(
            '❌ Error: Attribute not found for modal form data save: $attributeName');
        return;
      }

      final existingAttribute = entity.attributes![attributeIndex];
      ObjectAttribute updatedAttribute;

      // Update attribute based on form type
      switch (formType) {
        case 'NestedFunction':
          updatedAttribute = existingAttribute.copyWithNestedFunctionData(
            attributeName: formData['attributeName'] ?? '',
            functionType: formData['functionType'] ?? '',
            functionName: formData['functionName'] ?? '',
            functionInputs: formData['functionInputs'] ?? '',
            condition: formData['condition'] ?? '',
          );
          break;
        case 'DataMapping':
          // Convert display names to actual names before saving
          final actualSourceLO1 = formData['sourceLO1'] ?? '';
          final actualSourceLO2 = formData['sourceLO2'] ?? '';
          final actualSourceEntityAttribute1 = _convertDisplayNameToActualName(
              formData['sourceEntityAttribute1'] ?? '', context);
          final actualSourceEntityAttribute2 = _convertDisplayNameToActualName(
              formData['sourceEntityAttribute2'] ?? '', context);

          updatedAttribute = existingAttribute.copyWithDataMappingData(
            loName: formData['loName'] ?? '',
            sourceLO1: actualSourceLO1,
            sourceLO2: actualSourceLO2,
            sourceEntityAttribute1: actualSourceEntityAttribute1,
            sourceEntityAttribute2: actualSourceEntityAttribute2,
          );
          break;
        case 'SelectEntityAttribute':
          updatedAttribute =
              existingAttribute.copyWithSelectEntityAttributeData(
            sourceEntityAttribute: formData['sourceEntityAttribute'] ?? '',
            condition: formData['condition'] ?? '',
          );
          break;
        case 'ConditionPotentiality':
          updatedAttribute =
              existingAttribute.copyWithConditionPotentialityData(
            sourceEntity: formData['sourceEntity'] ?? '',
            sourceEntityAttribute: formData['sourceEntityAttribute'] ?? '',
          );
          break;
        default:
          Logger.info('❌ Unknown form type: $formType');
          return;
      }

      // Replace the attribute in the entity
      entity.attributes![attributeIndex] = updatedAttribute;

      Logger.info(
          '✅ Modal form data saved to attribute: $attributeName in entity: $entityId');
      Logger.info('   Form type: $formType');
      Logger.info('   Form data: $formData');
    } catch (e) {
      Logger.info('❌ Error saving modal form data to attribute: $e');
    }
  }

  /// Add modal form data to ObjectAttribute - now handled directly in modal save
  ObjectAttribute _addModalFormDataToAttribute(
      ObjectAttribute baseAttribute, String attributeName) {
    // Modal form data is now saved directly to ObjectAttribute when modal is saved
    // This method just returns the base attribute as-is
    Logger.info(
        '   ObjectAttribute ready for direct modal form data: $attributeName');
    return baseAttribute;
  }

  String _getDisplayTitle() {
    // For multiple objects, show count
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return '${widget.title} (${widget.selectedObjects!.length} objects)';
    }

    return widget.title;
  }

  String _getExpandedContentTitle() {
    // For multiple objects, show generic title
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return 'Input Stack Configuration (${widget.selectedObjects!.length} objects)';
    }

    return 'Input Stack Configuration';
  }

  int _getDynamicAttributeCount() {
    // For multiple objects, sum all attributes
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return widget.selectedObjects!
          .fold(0, (sum, obj) => sum + obj.attributes.length);
    }

    return widget.attributeCount;
  }

  @override
  void didUpdateWidget(LoInputStackAccordion oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Re-initialize multiple objects data if selectedObjects changed
    // Use deep comparison to detect actual content changes, not just reference changes
    if (_hasSelectedObjectsChanged(
        oldWidget.selectedObjects, widget.selectedObjects)) {
      Logger.info(
          '🔄 LoInputStackAccordion: Detected selectedObjects change, reinitializing data');
      _initializeMultipleObjectsData();
    }
  }

  /// Deep comparison to check if selectedObjects list has actually changed
  bool _hasSelectedObjectsChanged(
      List<SelectedObjectData>? oldList, List<SelectedObjectData>? newList) {
    // If both are null, no change
    if (oldList == null && newList == null) return false;

    // If one is null and the other isn't, there's a change
    if (oldList == null || newList == null) return true;

    // If lengths are different, there's a change
    if (oldList.length != newList.length) return true;

    // Check if all objects in the lists are the same (by ID and attributes)
    for (int i = 0; i < oldList.length; i++) {
      final oldObj = oldList[i];
      final newObj = newList[i];

      // Compare object IDs
      if (oldObj.id != newObj.id) return true;

      // Compare attributes lists
      if (oldObj.attributes.length != newObj.attributes.length) return true;

      for (int j = 0; j < oldObj.attributes.length; j++) {
        if (oldObj.attributes[j] != newObj.attributes[j]) return true;
      }
    }

    return false; // No changes detected
  }

  @override
  Widget build(BuildContext context) {
    // Ensure data consistency before building
    _ensureDataConsistency();

    return Consumer<GoDetailsProvider>(
      builder: (context, goDetailsProvider, child) {
        // Check if auto-expand is requested
        if (goDetailsProvider.shouldAutoExpandInputStack && !_isExpanded) {
          // Auto-expand the accordion
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _isExpanded = true;
            });
            widget.onExpansionChanged?.call(_isExpanded);

            // Reset the auto-expand flag
            goDetailsProvider.resetAutoExpandInputStack();
          });
        }

        final isExpanded =
            widget.accordionController?.isPanelExpanded('lo_input_stack') ??
                _isExpanded;

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          decoration: BoxDecoration(
            border: Border.all(
              color: isExpanded
                  ? const Color(0xFF0058FF)
                  : const Color(0xFFE5E7EB),
              width: isExpanded ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            children: [
              // Header Row
              InkWell(
                onTap: _toggleExpansion,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      // Title
                      Expanded(
                        child: Text(
                          widget.title,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight:
                                isExpanded ? FontWeight.w500 : FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),

                      // My Library Button
                      _buildMyLibraryButton(context),

                      const SizedBox(width: 12),

                      // Progress Indicator for Entity Addition
                      if (goDetailsProvider.isAddingEntity)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  const Color(0xFF0058FF),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                goDetailsProvider.addingEntityMessage,
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.labelSmall(context),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: const Color(0xFF0058FF),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                      const SizedBox(width: 12),

                      // Attributes Count Display
                      Text(
                        '${_getDynamicAttributeCount()} Attributes',
                        style: TextStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),

                      // // CP Toggle Switch
                      // _buildCPToggle(context),

                      const SizedBox(width: 12),

                      // Expansion Arrow
                      AnimatedRotation(
                        turns: isExpanded ? 0.5 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[600],
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Expanded Content
              if (isExpanded)
                Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                    color: Colors.white,
                  ),
                  child: _buildExpandedContent(context),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMyLibraryButton(BuildContext context) {
    return InkWell(
      onTap: widget.onMyLibraryPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFF0058FF),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.add,
              color: Colors.white,
              size: 14,
            ),
            const SizedBox(width: 4),
            Text(
              'My Library',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCPToggle(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Radio button (toggle switch style)
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _isCPToggleEnabled = !_isCPToggleEnabled;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 32,
              height: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: _isCPToggleEnabled
                    ? const Color(0xFF0058FF) // Blue for active state
                    : const Color(0xFF9CA3AF), // Gray for inactive state
              ),
              child: Stack(
                children: [
                  // Toggle circle with animation
                  AnimatedPositioned(
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInOut,
                    left:
                        _isCPToggleEnabled ? 18 : 2, // Move right when enabled
                    top: 2,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 6),
        // CP label
        Text(
          'CP',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // Widget _buildAttributesDropdown(BuildContext context) {
  //   return SizedBox(
  //     width: 120,
  //     child: CustomDropdownWidget(
  //       label: '${widget.attributeCount} Attributes',
  //       list: widget.availableAttributes.isNotEmpty
  //           ? widget.availableAttributes
  //           : _getDefaultAttributes(),
  //       value: '${widget.attributeCount} Attributes',
  //       onChanged: (value) {
  //         widget.onAttributeSelected?.call(value);
  //       },
  //     ),
  //   );
  // }

  List<String> _getDefaultAttributes() {
    return [
      '25 Attributes',
      '20 Attributes',
      '15 Attributes',
      '10 Attributes',
      '5 Attributes',
    ];
  }

  /// Show confirmation dialog before deleting a row from a specific object
  void _showDeleteConfirmationDialogForObject(
      BuildContext context, String objectId, int index) {
    final objectTableData = multipleObjectsData[objectId] ?? [];
    if (index >= objectTableData.length) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          title: Text(
            'Delete Row',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "${objectTableData[index].displayName}"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteRowFromObject(objectId, index);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Delete a row from a specific object's table
  void _deleteRowFromObject(String objectId, int index) {
    // Get the attribute name before removing it
    final objectTableData = multipleObjectsData[objectId];
    if (objectTableData == null || index >= objectTableData.length) {
      Logger.info(
          '❌ Invalid delete operation: objectId=$objectId, index=$index');
      return;
    }

    final attributeName = objectTableData[index].displayName as String;
    Logger.info(
        '🗑️ Deleting attribute: $attributeName from object: $objectId');

    // Remove from the table data
    setState(() {
      multipleObjectsData[objectId]?.removeAt(index);
    });

    // Also remove from the provider's selected objects list
    if (widget.loIndex != null) {
      try {
        final goDetailsProvider =
            Provider.of<GoDetailsProvider>(context, listen: false);
        goDetailsProvider.removeLoSelectedObjectAttribute(
          widget.loIndex!,
          objectId,
          attributeName,
        );
        Logger.info('✅ Removed attribute from provider');
      } catch (e) {
        Logger.info('❌ Error removing attribute from provider: $e');
      }
    }

    // Show a snackbar to confirm deletion
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Attribute "$attributeName" deleted successfully',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildExpandedContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getExpandedContentTitle(),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.bold,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              // CP Radio Toggle
              _buildCPToggle(context)
            ],
          ),
          const SizedBox(height: 16),

          // Multiple tables or single table
          _buildTablesContent(context),
        ],
      ),
    );
  }

  Widget _buildTablesContent(BuildContext context) {
    // Check if we have multiple objects
    if (widget.selectedObjects != null && widget.selectedObjects!.isNotEmpty) {
      return _buildMultipleTables(context);
    }

    // Show empty state when no objects are selected
    return _buildEmptyState(context);
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(4),
        color: Colors.grey[50],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No objects selected',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select objects from the library to configure input stack attributes',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleTables(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.selectedObjects!.map((objectData) {
        return Container(
          margin: const EdgeInsets.only(bottom: 24), // Space between tables
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Object title row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    objectData.object['displayName'] ??
                        objectData.object['name'] ??
                        'Unknown Object',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  // Remove button for this object
                  if (widget.onRemoveSelectedObject != null &&
                      widget.loIndex != null)
                    InkWell(
                      onTap: () => widget.onRemoveSelectedObject!(
                          widget.loIndex!, objectData.id),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.red,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              // Table for this object
              _buildObjectTable(context, objectData),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildObjectEmptyState(
      BuildContext context, SelectedObjectData objectData) {
    final objectName = objectData.object['displayName'] ??
        objectData.object['name'] ??
        'Unknown Object';

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Object Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFFF9FAFB),
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(4)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    objectName,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleSmall(context),
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ),
                Text(
                  '${objectData.attributes.length} attributes',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          // Empty State Content
          Container(
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius:
                  const BorderRadius.vertical(bottom: Radius.circular(4)),
              color: Colors.grey[50],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.warning_outlined,
                    size: 32,
                    color: Colors.orange[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No data available for this object',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Object data may need to be reinitialized',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildObjectTable(
      BuildContext context, SelectedObjectData objectData) {
    // Check if data exists for this object
    if (!multipleObjectsData.containsKey(objectData.id) ||
        multipleObjectsData[objectData.id]!.isEmpty) {
      Logger.info(
          '⚠️ _buildObjectTable: No data found for ${objectData.id}, showing empty state...');
      return _buildObjectEmptyState(context, objectData);
    }

    final objectTableData = multipleObjectsData[objectData.id] ?? [];
    Logger.info(
        '📊 _buildObjectTable: Building table for ${objectData.id} with ${objectTableData.length} rows');

    // Dropdown options
    final dataSourceOptions = [
      'User',
      'Nested Function',
      'Mapping',
      'Constant',
      'Condition Potential',
    ];

    // final uiControlOptions = [
    //   'text',
    //   'label',
    //   'email',
    //   'Alpha Numerical',
    //   'Dropdown',
    //   'Text',
    //   'Number',
    // ];
    final uiControlOptions = UIControllerTypeMapping.uiTypeToControllers;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // Scrollable content (unified scrolling)
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: IntrinsicWidth(
                child: Column(
                  children: [
                    // Header
                    Container(
                      height: 40,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: const BoxDecoration(
                        color: Color(0xFFF9FAFB),
                        borderRadius:
                            BorderRadius.vertical(top: Radius.circular(4)),
                      ),
                      child: Row(
                        children: [
                          _buildHeaderCell(context, 'DISPLAY NAME', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'DATA SOURCE', 160),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'FUNCTION TYPE', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'STATUS', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'UI CONTROL', 120),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'HELPER TEXT', 140),
                          const SizedBox(width: 24),
                          _buildHeaderCell(context, 'ERROR MESSAGE', 140),
                        ],
                      ),
                    ),
                    // Rows
                    if (objectTableData.isEmpty)
                      Container(
                        height: 100,
                        padding: const EdgeInsets.all(16),
                        child: Center(
                          child: Text(
                            'No attributes found for this object.',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    else
                      ...objectTableData.asMap().entries.map((entry) {
                        final index = entry.key;
                        final input = entry.value;
                        return Container(
                          height: 50, // Fixed height to match design
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color:
                                Colors.white, // All rows white - no alternating
                            border: Border(
                              bottom: BorderSide(
                                color: index < objectTableData.length - 1
                                    ? const Color(0xFFE5E7EB)
                                    : Colors.transparent,
                              ),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildDataCell(
                                  context, input.displayName ?? '', 140),
                              const SizedBox(width: 24),
                              _buildDataSourceDropdownCell(
                                  context,
                                  input.dataSource ?? '',
                                  index == 0
                                      ? dataSourceOptions
                                          .where(
                                              (option) => option != 'Mapping')
                                          .toList()
                                      : dataSourceOptions,
                                  160, (value) {
                                final functionType =
                                    _getDefaultFunctionTypeForDataSource(value);
                                setState(() {
                                  multipleObjectsData[objectData.id]?[index]
                                      .dataSource = value;
                                  // Update function type based on data source selection
                                  final functionType =
                                      _getDefaultFunctionTypeForDataSource(
                                          value);
                                  multipleObjectsData[objectData.id]?[index]
                                      .functionType = functionType;

                                  // Persist state for multiple objects
                                  final displayName =
                                      multipleObjectsData[objectData.id]?[index]
                                              .displayName ??
                                          '';
                                  _persistedMultipleObjectDataSources[
                                      objectData.id] ??= {};
                                  _persistedMultipleObjectFunctionTypes[
                                      objectData.id] ??= {};
                                  _persistedMultipleObjectDataSources[
                                      objectData.id]?[displayName] = value;
                                  _persistedMultipleObjectFunctionTypes[
                                          objectData.id]?[displayName] =
                                      functionType;
                                });

                                // Only show dialog for data sources that support modal configuration
                                // Don't show modal for "User" and "Condition Potential"
                                if (value.toLowerCase() != 'user' &&
                                    value.toLowerCase() !=
                                        'condition potential') {
                                  _showDialogBasedOnDataSource(
                                    context,
                                    value,
                                    functionType,
                                    rowIndex: index,
                                    objectId: objectData.id,
                                  );
                                }

                                // Save data to provider after state update
                                _saveEntityTableData();
                              }),
                              const SizedBox(width: 24),
                              _buildDynamicFunctionTypeCellForObject(
                                  context,
                                  input.functionType ?? '',
                                  input.dataSource ?? '',
                                  120,
                                  index,
                                  objectData.id),
                              const SizedBox(width: 24),
                              _buildStatusCell(
                                  context,
                                  (input.required ?? false)
                                      ? 'Required'
                                      : 'Optional',
                                  120,
                                  index,
                                  objectId: objectData.id),
                              const SizedBox(width: 24),
                              _buildDropdownCell(
                                  context,
                                  input.uiControl ?? '',
                                  uiControlOptions[
                                          '${input.dataType?.toLowerCase()}'] ??
                                      [],
                                  120, (value) {
                                setState(() {
                                  multipleObjectsData[objectData.id]?[index]
                                      .uiControl = value;
                                });
                                // Save data to provider after state update
                                _saveEntityTableData();
                              }),
                              const SizedBox(width: 24),
                              _buildEditableCellForObject(
                                  context, objectData.id, index, true, 140),
                              const SizedBox(width: 24),
                              _buildEditableCellForObject(
                                  context, objectData.id, index, false, 140),
                            ],
                          ),
                        );
                      }),
                  ],
                ),
              ),
            ),
          ),
          // Fixed/Sticky Actions column
          Container(
            width: 80,
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB)),
              ),
            ),
            child: Column(
              children: [
                // Actions Header
                Container(
                  height: 40, // Match table header height exactly
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF9FAFB),
                    borderRadius:
                        BorderRadius.only(topRight: Radius.circular(4)),
                  ),
                  child: Center(
                    child: Text(
                      'ACTIONS',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                // Action buttons for each row or empty state
                if (objectTableData.isEmpty)
                  Container(
                    height: 100,
                    child: const SizedBox.shrink(),
                  )
                else
                  ...objectTableData.asMap().entries.map((entry) {
                    final index = entry.key;
                    return Container(
                      height: 50, // Match table row height exactly
                      padding: const EdgeInsets.symmetric(
                          horizontal: 0, vertical: 0),
                      decoration: BoxDecoration(
                        color: Colors.white, // All white to match table rows
                        border: Border(
                          bottom: BorderSide(
                            color: index < objectTableData.length - 1
                                ? const Color(0xFFE5E7EB)
                                : Colors.transparent,
                          ),
                        ),
                      ),
                      child: Center(
                        child: InkWell(
                          onTap: () {
                            _showDeleteConfirmationDialogForObject(
                                context, objectData.id, index);
                          },
                          borderRadius: BorderRadius.circular(2),
                          child: Container(
                              child: const Icon(
                            Icons.delete_outline,
                            size: 16,
                            color: Colors.red,
                          )),
                        ),
                      ),
                    );
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String title, double width) {
    return SizedBox(
      width: width,
      child: Text(
        title,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w600,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildDataCell(BuildContext context, String data, double width) {
    return SizedBox(
      width: width,
      child: Text(
        data,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildStatusCell(
      BuildContext context, String status, double width, int index,
      {String? objectId}) {
    final statusOptions = [
      'Required',
      'Optional',
    ];

    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: status.isEmpty ? 'Select...' : status,
        list: statusOptions,
        value: status.isEmpty ? null : status,
        onChanged: (value) {
          setState(() {
            if (objectId != null) {
              // Update for multiple objects table
              multipleObjectsData[objectId]![index].required =
                  value == 'Required' ? true : false;
            }
          });
          // Save data to provider after state update
          _saveEntityTableData();
        },
      ),
    );
  }

  Widget _buildErrorCell(BuildContext context, String error, double width) {
    return SizedBox(
      width: width,
      child: Text(
        error,
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.labelSmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: error.isNotEmpty ? const Color(0xFFFF4444) : Colors.black,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildEditableCellForObject(BuildContext context, String objectId,
      int index, bool isHelperText, double width) {
    final controllers = isHelperText
        ? multipleHelperTextControllers[objectId]
        : multipleErrorMessageControllers[objectId];

    final controller = (controllers != null && index < controllers.length)
        ? controllers[index]
        : null;

    // If controller is null, return a static text cell as fallback
    if (controller == null) {
      return _buildDataCell(context, '', width);
    }

    // Get the object table data for validation
    final objectTableData = multipleObjectsData[objectId] ?? [];

    return SizedBox(
      width: width,
      child: Container(
        height: 35, // Match the CustomDropdownWidget height
        child: Directionality(
          textDirection: TextDirection.ltr,
          child: TextField(
            controller: controller,
            textDirection: TextDirection.ltr,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(2),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              filled: false,
              isDense: true,
              hintText: isHelperText
                  ? (index < objectTableData.length
                      ? objectTableData[index].displayName != null
                          ? 'Enter ${objectTableData[index].displayName}'
                          : 'Enter attribute name'
                      : 'Enter attribute name')
                  : (index < objectTableData.length
                      ? objectTableData[index].displayName != null
                          ? 'Error message for ${objectTableData[index].displayName}'
                          : 'Error message for attribute name'
                      : 'Error message for attribute name'),
              hintStyle: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade500,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDropdownCell(BuildContext context, String currentValue,
      List<String> options, double width, Function(String) onChanged) {
    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: currentValue.isEmpty ? 'Select...' : currentValue,
        list: options,
        value: currentValue.isEmpty ? null : currentValue,
        onChanged: (value) {
          onChanged(value);
        },
      ),
    );
  }

  Widget _buildDataSourceDropdownCell(BuildContext context, String currentValue,
      List<String> options, double width, Function(String) onChanged) {
    return SizedBox(
      width: width,
      child: CustomDropdownWidget(
        label: currentValue.isEmpty ? 'User' : currentValue,
        list: options,
        value: currentValue.isEmpty ? 'User' : currentValue,
        onChanged: (value) {
          onChanged(value);
        },
      ),
    );
  }

  Widget _buildDynamicFunctionTypeCell(BuildContext context,
      String functionType, String dataSource, double width, int index) {
    return SizedBox(
      width: width,
      child: Row(
        children: [
          // Icon based on data source
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: _getFunctionTypeIconByDataSource(dataSource),
          ),
          // Function type text/link based on data source
          Expanded(
            child: _buildFunctionTypeContent(
                context, functionType, dataSource, index),
          ),
        ],
      ),
    );
  }

  Widget _buildDynamicFunctionTypeCellForObject(
      BuildContext context,
      String functionType,
      String dataSource,
      double width,
      int index,
      String objectId) {
    return SizedBox(
      width: width,
      child: Row(
        children: [
          // Icon based on data source
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: _getFunctionTypeIconByDataSource(dataSource),
          ),
          // Function type text/link based on data source
          Expanded(
            child: _buildFunctionTypeContent(
                context, functionType, dataSource, index,
                objectId: objectId),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionTypeCell(
      BuildContext context, String functionType, double width) {
    return SizedBox(
      width: width,
      child: Row(
        children: [
          // Icon based on data source
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: _getFunctionTypeIcon(functionType),
          ),
          // Function type text/link based on data source
          Expanded(
            child: Text(
              functionType,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Show unified modal with dynamic content based on selection
  void _showUnifiedModal(
    BuildContext context,
    String functionType, {
    int? rowIndex,
    String? objectId,
    Function(String)? onFunctionTypeChanged,
  }) {
    Logger.info(
        'Showing modal for function type: $functionType'); // Debug Logger.info

    // Get the attribute name and indices from the table data
    String? attributeName;
    int? entityIndex;
    int? attributeIndex;

    if (objectId != null && rowIndex != null) {
      final objectTableData = multipleObjectsData[objectId];
      if (objectTableData != null && rowIndex < objectTableData.length) {
        attributeName = objectTableData[rowIndex].displayName;

        // Find entity and attribute indices by matching the objectId and rowIndex
        // The objectId corresponds to the entity, and rowIndex corresponds to the attribute
        try {
          final goDetailsProvider =
              Provider.of<GoDetailsProvider>(context, listen: false);
          final goModel = goDetailsProvider.currentGoModel;

          if (goModel?.localObjectivesList != null &&
              widget.loIndex != null &&
              widget.loIndex! < goModel!.localObjectivesList!.length) {
            final currentLO = goModel.localObjectivesList![widget.loIndex!];
            if (currentLO.entitiesList != null) {
              // Find entity index by matching objectId (entity ID or name)
              for (int i = 0; i < currentLO.entitiesList!.length; i++) {
                final entity = currentLO.entitiesList![i];
                if (entity.id == objectId || entity.name == objectId) {
                  entityIndex = i;

                  // Find attribute index by matching rowIndex
                  if (entity.attributes != null &&
                      rowIndex < entity.attributes!.length) {
                    attributeIndex = rowIndex;
                  }
                  break;
                }
              }
            }
          }
        } catch (e) {
          Logger.info('❌ Error finding entity/attribute indices: $e');
        }
      }
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _UnifiedModal(
          initialFunctionType: functionType,
          rowIndex: rowIndex,
          objectId: objectId,
          onFunctionTypeChanged: onFunctionTypeChanged,
          loIndex: widget.loIndex, // Pass the loIndex from the parent widget
          attributeName: attributeName, // Pass the attribute name
          entityIndex: entityIndex, // Pass the entity index
          attributeIndex: attributeIndex, // Pass the attribute index
        );
      },
    );
  }

  Widget _getFunctionTypeIcon(String functionType) {
    switch (functionType.toLowerCase()) {
      case 'send email':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'nested function':
      case 'lo.mig_entity.attribute':
      case 'address 11':
      case 'entity.attribute':
        return SvgPicture.asset(
          'assets/images/my_library/user.svg',
          width: 16,
          height: 16,
        );
      default:
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
    }
  }

  Widget _getFunctionTypeIconByDataSource(String dataSource) {
    // Handle empty data source
    if (dataSource.isEmpty) {
      return SvgPicture.asset(
        'assets/images/my_library/assistant.svg',
        width: 16,
        height: 16,
      );
    }

    switch (dataSource.toLowerCase()) {
      case 'user':
        return SvgPicture.asset(
          'assets/images/my_library/user.svg',
          width: 16,
          height: 16,
        );
      case 'nested function':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'mapping':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'constant':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      case 'conditional potential':
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
      default:
        return SvgPicture.asset(
          'assets/images/my_library/assistant.svg',
          width: 16,
          height: 16,
        );
    }
  }

  Widget _buildFunctionTypeContent(
      BuildContext context, String functionType, String dataSource, int index,
      {String? objectId}) {
    // For empty/unselected data source, show "N/A" without any dialog functionality
    if (dataSource.isEmpty) {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // For "User" and "Condition Potential" data sources, show "N/A" without any dialog functionality
    if (dataSource.toLowerCase() == 'user' ||
        dataSource.toLowerCase() == 'condition potential') {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    // For all other data sources, show clickable element that opens respective dialogs
    if (functionType.isNotEmpty) {
      return InkWell(
        onTap: () {
          _showDialogBasedOnDataSource(context, dataSource, functionType,
              rowIndex: index, objectId: objectId);
        },
        child: Text(
          functionType,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: const Color(0xFF0058FF), // Blue color for links
            decoration: TextDecoration.underline,
          ).copyWith(
            decorationColor: const Color(0xFF0058FF),
          ),
          overflow: TextOverflow.ellipsis,
        ),
      );
    } else {
      return Text(
        'N/A',
        style: FontManager.getCustomStyle(
          fontSize: ResponsiveFontSizes.bodySmall(context),
          fontWeight: FontWeight.w400,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  void _showDialogBasedOnDataSource(
    BuildContext context,
    String dataSource,
    String functionType, {
    int? rowIndex,
    String? objectId,
  }) {
    // Don't show modal for "User" and "Condition Potential" data sources
    if (dataSource.toLowerCase() == 'user' ||
        dataSource.toLowerCase() == 'condition potential') {
      Logger.info('Modal popup disabled for data source: $dataSource');
      return;
    }

    // Create callback to update function type in the table
    onFunctionTypeChanged(String newFunctionType) {
      setState(() {
        if (objectId != null && rowIndex != null) {
          // Update multiple objects data
          if (multipleObjectsData[objectId] != null &&
              rowIndex < multipleObjectsData[objectId]!.length) {
            multipleObjectsData[objectId]![rowIndex].functionType =
                newFunctionType;

            // Update persisted state
            final displayName =
                multipleObjectsData[objectId]![rowIndex].displayName!;
            _persistedMultipleObjectFunctionTypes[objectId] ??= {};
            _persistedMultipleObjectFunctionTypes[objectId]![displayName] =
                newFunctionType;
          }
        }
      });
      // Save data to provider after state update
      _saveEntityTableData();
    }

    switch (dataSource.toLowerCase()) {
      case 'nested function':
        _showUnifiedModal(
          context,
          functionType.isNotEmpty ? functionType : 'Send email',
          rowIndex: rowIndex,
          objectId: objectId,
          onFunctionTypeChanged: onFunctionTypeChanged,
        );
        break;
      case 'mapping':
        _showUnifiedModal(
          context,
          'LO.mig_Entity.Attribute',
          rowIndex: rowIndex,
          objectId: objectId,
          onFunctionTypeChanged: onFunctionTypeChanged,
        );
        break;
      case 'constant':
        _showUnifiedModal(
          context,
          'Address 11',
          rowIndex: rowIndex,
          objectId: objectId,
          onFunctionTypeChanged: onFunctionTypeChanged,
        );
        break;
      default:
        // For any other case, use the existing function type
        _showUnifiedModal(
          context,
          functionType,
          rowIndex: rowIndex,
          objectId: objectId,
          onFunctionTypeChanged: onFunctionTypeChanged,
        );
        break;
    }
  }

  String _getDefaultFunctionTypeForDataSource(String dataSource) {
    switch (dataSource.toLowerCase()) {
      case 'user':
        return ''; // Empty for N/A display
      case 'condition potential':
        return ''; // Empty for N/A display
      case 'nested function':
        return 'Send email';
      case 'mapping':
        return 'LO.mig_Entity.Attribute';
      case 'constant':
        return 'Address 11';
      default:
        return '';
    }
  }

  Widget _buildLinkCell(BuildContext context, String data, double width) {
    return SizedBox(
      width: width,
      child: data.isNotEmpty
          ? InkWell(
              onTap: () {
                // Handle link click - could navigate or show details
                Logger.info('Link clicked: $data');
              },
              child: Text(
                data,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: const Color(0xFF0058FF), // Blue color for links
                  decoration: TextDecoration.underline,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            )
          : Text(
              data,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black87,
              ),
              overflow: TextOverflow.ellipsis,
            ),
    );
  }

  /// Build custom tooltip with enhanced styling and positioning
  Widget _buildCustomTooltip({
    required String message,
    required Widget child,
  }) {
    return Tooltip(
      message: message,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      textStyle: FontManager.getCustomStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w400,
        fontFamily: FontManager.fontFamilyTiemposText,
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      margin: const EdgeInsets.all(4),
      preferBelow: true,
      verticalOffset: 8,
      waitDuration: const Duration(milliseconds: 500),
      showDuration: const Duration(seconds: 3),
      child: child,
    );
  }

  /// Generate dynamic LO options for dropdown based on current LO index
  /// Shows only previous LOs according to business logic for DataMapping
  List<String> _getDynamicLOOptions(BuildContext context) {
    try {
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);

      // Get current LO index from widget parameter
      final currentLoIndex = widget.loIndex;

      if (currentLoIndex == null) {
        return ['No LOs available'];
      }

      final currentGoModel = goDetailsProvider.currentGoModel;
      if (currentGoModel?.localObjectivesList == null) {
        return ['No LOs available'];
      }

      // Get previous LOs only (LOs that come before the current LO)
      final previousLOOptions = <String>[];

      // Iterate through all LOs before the current LO
      for (int i = 0; i < currentLoIndex; i++) {
        if (i < currentGoModel!.localObjectivesList!.length) {
          final loName =
              currentGoModel.localObjectivesList![i].name ?? 'LO-${i + 1}';
          previousLOOptions.add(loName);
        }
      }

      return previousLOOptions.isNotEmpty
          ? previousLOOptions
          : ['No previous LOs available'];
    } catch (e) {
      // Fallback to hardcoded options if provider access fails
      return ['Cash', 'Credit', 'Debit'];
    }
  }
}

/// Unified Modal Widget that changes content based on dropdown selection
class _UnifiedModal extends StatefulWidget {
  final String initialFunctionType;
  final int? rowIndex;
  final String? objectId;
  final Function(String)? onFunctionTypeChanged;
  final int? loIndex; // Add loIndex parameter
  final String? attributeName; // Add attribute name parameter
  final int? entityIndex; // Add entity index parameter
  final int? attributeIndex; // Add attribute index parameter

  const _UnifiedModal({
    required this.initialFunctionType,
    this.rowIndex,
    this.objectId,
    this.onFunctionTypeChanged,
    this.loIndex, // Add loIndex parameter
    this.attributeName, // Add attribute name parameter
    this.entityIndex, // Add entity index parameter
    this.attributeIndex, // Add attribute index parameter
  });

  @override
  State<_UnifiedModal> createState() => _UnifiedModalState();
}

class _UnifiedModalState extends State<_UnifiedModal> {
  late String selectedModalType;

  // Form data objects instead of controllers
  late NestedFunctionFormData _nestedFunctionData;
  late DataMappingFormData _dataMappingData;
  late SelectEntityAttributeFormData _selectEntityAttributeData;
  late ConditionPotentialityFormData _conditionPotentialityData;

  /// Convert display names to actual names for data storage
  /// Converts "Furniture Order.Payment Method" to "FurnitureOrder.payment_method"
  String _convertDisplayNameToActualName(
      String displayValue, BuildContext context) {
    try {
      // Skip conversion if the value is empty or a placeholder
      if (displayValue.isEmpty || displayValue == 'Select Entity.Attribute') {
        return displayValue;
      }

      // Try to use MyLibraryService for more accurate conversion
      if (MyLibraryService.isInitializedForDynamicAttributes) {
        final result = _convertUsingLibraryService(displayValue);
        if (result != displayValue) {
          Logger.info('🔄 Library conversion: "$displayValue" to "$result"');
          return result;
        }
      }

      // Fallback to local data conversion
      final result = _convertUsingLocalData(displayValue, context);
      if (result != displayValue) {
        Logger.info('🔄 Local conversion: "$displayValue" to "$result"');
        return result;
      }

      Logger.info('⚠️ No conversion found for "$displayValue", using as-is');
      return displayValue; // Return as-is if no match found
    } catch (e) {
      Logger.info('❌ Error converting display name: $e');
      return displayValue; // Return as-is on error
    }
  }

  /// Convert display names to separated entity and attribute names
  /// Converts "Furniture Order.Payment Method" to {"entity": "FurnitureOrder", "attribute": "payment_method"}
  Map<String, String> _convertDisplayNameToSeparatedNames(
      String displayValue, BuildContext context) {
    try {
      // Skip conversion if the value is empty or a placeholder
      if (displayValue.isEmpty || displayValue == 'Select Entity.Attribute') {
        return {'entity': '', 'attribute': ''};
      }

      // Parse the display value to extract entity and attribute display names
      if (!displayValue.contains('.')) {
        return {
          'entity': displayValue,
          'attribute': ''
        }; // Not in expected format
      }

      final parts = displayValue.split('.');
      if (parts.length != 2) {
        return {
          'entity': displayValue,
          'attribute': ''
        }; // Not in expected format
      }

      final entityDisplayName = parts[0];
      final attributeDisplayName = parts[1];

      // Convert display names to actual names using naming conventions
      final actualEntityName =
          _convertEntityDisplayNameToActualName(entityDisplayName);
      final actualAttributeName =
          _convertAttributeDisplayNameToActualName(attributeDisplayName);

      Logger.info(
          '🔄 Separated conversion: "$displayValue" to entity: "$actualEntityName", attribute: "$actualAttributeName"');

      return {
        'entity': actualEntityName,
        'attribute': actualAttributeName,
      };
    } catch (e) {
      Logger.info('❌ Error converting display name to separated names: $e');
      return {'entity': '', 'attribute': ''}; // Return empty on error
    }
  }

  /// Convert using MyLibraryService data (more accurate)
  String _convertUsingLibraryService(String displayValue) {
    try {
      // Parse the display value to extract entity and attribute display names
      if (!displayValue.contains('.')) {
        return displayValue; // Not in expected format
      }

      final parts = displayValue.split('.');
      if (parts.length != 2) {
        return displayValue; // Not in expected format
      }

      final entityDisplayName = parts[0];
      final attributeDisplayName = parts[1];

      // Convert display names to actual names using naming conventions
      final actualEntityName =
          _convertEntityDisplayNameToActualName(entityDisplayName);
      final actualAttributeName =
          _convertAttributeDisplayNameToActualName(attributeDisplayName);

      final actualValue = '$actualEntityName.$actualAttributeName';

      // Only return the converted value if it's different from the original
      if (actualValue != displayValue) {
        return actualValue;
      }

      return displayValue; // No conversion needed
    } catch (e) {
      Logger.info('❌ Error in library service conversion: $e');
      return displayValue;
    }
  }

  /// Convert entity display name to actual name
  /// "Furniture Order" -> "FurnitureOrder"
  String _convertEntityDisplayNameToActualName(String displayName) {
    // Remove spaces and convert to PascalCase
    return displayName
        .split(' ')
        .map((word) => word.isNotEmpty
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : '')
        .join('');
  }

  /// Convert attribute display name to actual name
  /// "Payment Method" -> "payment_method"
  String _convertAttributeDisplayNameToActualName(String displayName) {
    // Convert to snake_case
    return displayName.toLowerCase().replaceAll(' ', '_');
  }

  /// Convert using local GO model data (fallback)
  String _convertUsingLocalData(String displayValue, BuildContext context) {
    // This is a simplified fallback - in practice, you might want to implement
    // more sophisticated logic here if needed
    return displayValue;
  }

  /// Populate target context information for DataMapping
  void _populateTargetContextInfo() {
    try {
      // Get GoDetailsProvider and current GoModel
      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel == null) {
        Logger.info('❌ GoModel is null, cannot populate target context');
        return;
      }

      // Get the current LO index from widget
      final loIndex = widget.loIndex;
      if (loIndex == null ||
          goModel.localObjectivesList == null ||
          loIndex >= goModel.localObjectivesList!.length) {
        Logger.info('❌ Invalid LO index or localObjectivesList');
        return;
      }

      final currentLO = goModel.localObjectivesList![loIndex];
      final targetLOName = currentLO.name ?? 'LO-${loIndex + 1}';

      // Get the target entity and attribute from widget parameters
      final entityIndex = widget.entityIndex;
      final attributeIndex = widget.attributeIndex;

      if (entityIndex == null || attributeIndex == null) {
        Logger.info('❌ Entity or attribute index is null');
        return;
      }

      if (currentLO.entitiesList == null ||
          entityIndex >= currentLO.entitiesList!.length) {
        Logger.info('❌ Invalid entity index or entitiesList');
        return;
      }

      final targetEntity = currentLO.entitiesList![entityIndex];
      final targetEntityName =
          targetEntity.name ?? targetEntity.displayName ?? 'Unknown Entity';

      if (targetEntity.attributes == null ||
          attributeIndex >= targetEntity.attributes!.length) {
        Logger.info('❌ Invalid attribute index or attributes list');
        return;
      }

      final targetAttribute = targetEntity.attributes![attributeIndex];
      final targetAttributeName = targetAttribute.name ??
          targetAttribute.displayName ??
          'Unknown Attribute';

      // Update the DataMapping form data with target context
      _dataMappingData = _dataMappingData.copyWith(
        targetLO: targetLOName,
        targetEntity: targetEntityName,
        targetAttribute: targetAttributeName,
      );

      Logger.info(
          '✅ Target context populated: LO="$targetLOName", Entity="$targetEntityName", Attribute="$targetAttributeName"');
    } catch (e) {
      Logger.info('❌ Error populating target context: $e');
    }
  }

  // Text controllers for UI binding (still needed for TextField widgets)
  final Map<String, TextEditingController> _textControllers = {};

  // Dynamic attributes state
  List<String> _dynamicAttributeOptions = [];
  bool _isLoadingAttributes = false;

  // State variables for Calculate function conditional visibility
  bool _showCalculateDropdowns = false;
  bool _showSourceSection = false;
  // State variables for CustomInputWithDropdown
  String _selectedEntityAttribute = '';
  String _expressionInput = '';

  @override
  void initState() {
    super.initState();
    selectedModalType =
        _getModalTypeFromFunctionType(widget.initialFunctionType);
    _initializeFormData();
    _initializeTextControllers();
    _initializeAttributesService();
  }

  @override
  void dispose() {
    // Dispose all text controllers
    for (var controller in _textControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  String _getModalTypeFromFunctionType(String functionType) {
    Logger.info('Function type received: "$functionType"'); // Debug Logger.info

    // Map function types to their corresponding modal types
    switch (functionType.toLowerCase()) {
      case 'lo.mig_entity.attribute':
        return 'Add Data Mapping';
      case 'address 11':
        return 'Select Entity.attribute';
      case 'entity.attribute':
        return 'Condition Potentiality';
      case 'condition potential':
        return 'Condition Potentiality';
      case 'nested function':
      case 'send email':
      default:
        return 'Nested Function Configuration';
    }
  }

  /// Save current form data directly to ObjectAttribute in GoModel
  void _saveFormData() {
    Logger.info('🔄 Saving modal form data directly to ObjectAttribute...');

    try {
      // Get GoDetailsProvider and current GoModel
      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel == null) {
        Logger.info('❌ GoModel is null, cannot save modal form data');
        return;
      }

      // Get the current LO index from widget
      final loIndex = widget.loIndex;
      if (loIndex == null ||
          goModel.localObjectivesList == null ||
          loIndex >= goModel.localObjectivesList!.length) {
        Logger.info('❌ Invalid LO index or localObjectivesList');
        return;
      }

      final lo = goModel.localObjectivesList![loIndex];
      if (lo.entitiesList == null || lo.entitiesList!.isEmpty) {
        Logger.info('❌ No entities found in LO');
        return;
      }

      // Find the specific entity and attribute being edited
      final targetEntity = _findTargetEntity(lo.entitiesList!);
      if (targetEntity == null) {
        Logger.info('❌ Target entity not found: ${widget.objectId}');
        return;
      }

      final targetAttributeIndex = _findTargetAttributeIndex(targetEntity);
      if (targetAttributeIndex == -1) {
        Logger.info(
            '❌ Target attribute not found at rowIndex: ${widget.rowIndex}');
        return;
      }

      final targetAttribute = targetEntity.attributes![targetAttributeIndex];

      // Update the specific attribute with modal form data
      targetEntity.attributes![targetAttributeIndex] =
          _createUpdatedAttributeWithModalData(targetAttribute);

      Logger.info(
          '✅ Modal form data saved to ObjectAttribute: ${targetAttribute.displayName} (Entity: ${targetEntity.displayName ?? targetEntity.name}, Row: ${widget.rowIndex})');
    } catch (e) {
      Logger.info('❌ Error saving modal form data: $e');
    }
  }

  /// Initialize form data objects with default values or load from persistence
  void _initializeFormData() {
    // Initialize form data objects
    String initialFunctionType = '';
    if (selectedModalType == 'Nested Function Configuration') {
      // Map the initial function type to dropdown values
      switch (widget.initialFunctionType.toLowerCase()) {
        case 'send email':
          initialFunctionType = 'Send Email';
          break;
        case 'conditional assignment':
          initialFunctionType = 'Conditional Assignment';
          break;
        case 'validate data':
          initialFunctionType = 'Validate Data';
          break;
        case 'create record':
          initialFunctionType = 'Create Record';
          break;
        case 'update record':
          initialFunctionType = 'Update Record';
          break;
        default:
          initialFunctionType = 'Send Email'; // Default fallback
      }
    }

    // Initialize form data objects with defaults
    _nestedFunctionData =
        NestedFunctionFormData(functionType: initialFunctionType);
    _dataMappingData = DataMappingFormData();
    _selectEntityAttributeData = SelectEntityAttributeFormData();
    _conditionPotentialityData = ConditionPotentialityFormData();

    // Populate target context information for DataMapping
    _populateTargetContextInfo();

    // Load existing data from ObjectAttribute if available
    _loadModalFormDataFromObjectAttribute();
  }

  /// Initialize text controllers for UI binding
  void _initializeTextControllers() {
    // Initialize text controllers based on current form data
    _textControllers['nestedFunction_condition'] =
        TextEditingController(text: _nestedFunctionData.condition);
    _textControllers['selectEntity_condition'] =
        TextEditingController(text: _selectEntityAttributeData.condition);

    // Add listeners to sync controller changes with form data
    _textControllers['nestedFunction_condition']?.addListener(() {
      _nestedFunctionData = _nestedFunctionData.copyWith(
        condition: _textControllers['nestedFunction_condition']?.text ?? '',
      );
    });

    _textControllers['selectEntity_condition']?.addListener(() {
      _selectEntityAttributeData = _selectEntityAttributeData.copyWith(
        condition: _textControllers['selectEntity_condition']?.text ?? '',
      );
    });
  }

  /// Initialize the dynamic attributes for the modal
  Future<void> _initializeAttributesService() async {
    setState(() {
      _isLoadingAttributes = true;
    });

    try {
      // Initialize the service if not already initialized
      if (!MyLibraryService.isInitializedForDynamicAttributes) {
        Logger.info(
            '🔄 Initializing MyLibraryService for dynamic attributes...');
        final initialized =
            await MyLibraryService.initializeForDynamicAttributes();
        if (!initialized) {
          Logger.info('❌ Failed to initialize MyLibraryService');
          throw Exception('Failed to initialize MyLibraryService');
        }
        Logger.info('✅ MyLibraryService initialized successfully');
      }

      // Get published attribute names for dropdown (only from postgres collections)
      final attributeNames = MyLibraryService.getPublishedAttributeNames();
      Logger.info(
          '📋 Retrieved ${attributeNames.length} published attribute names');

      setState(() {
        _dynamicAttributeOptions = attributeNames.isNotEmpty
            ? attributeNames
            : MyLibraryService.getFallbackAttributeNames();
        _isLoadingAttributes = false;
      });

      Logger.info(
          '✅ Dynamic attributes loaded successfully: ${_dynamicAttributeOptions.length} options');
    } catch (e) {
      // Use fallback options if service initialization fails
      Logger.info('❌ Error initializing dynamic attributes in modal: $e');
      setState(() {
        _dynamicAttributeOptions = MyLibraryService.getFallbackAttributeNames();
        _isLoadingAttributes = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Logger.info(
        'Building modal with type: $selectedModalType'); // Debug Logger.info

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 24,
      child: Container(
        width: _getModalWidth(),
        height: _getModalHeight(),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 24,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with drop shadow
            Container(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    selectedModalType,
                    style: FontManager.getCustomStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, size: 20),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // Dynamic content based on selected modal type
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: SingleChildScrollView(
                  child: _buildModalContent(),
                ),
              ),
            ),

            // Footer buttons with drop shadow
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () {
                      _saveFormData(); // Save form data before closing
                      Navigator.of(context).pop();
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey[400]!, width: 1),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isFormValid()
                        ? () {
                            // Handle save/apply action
                            _saveFormData(); // Save form data before closing
                            Navigator.of(context).pop();
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFormValid()
                          ? const Color(0xFF0058FF)
                          : Colors.grey[300],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      _getButtonText(),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _getModalWidth() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 550;
      case 'Select Entity.attribute':
        return 500;
      case 'Condition Potentiality':
        return 550;
      default:
        return 650;
    }
  }

  double _getModalHeight() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 480;
      case 'Select Entity.attribute':
        return 480;
      case 'Condition Potentiality':
        return 350;
      default:
        return 486;
    }
  }

  String _getButtonText() {
    switch (selectedModalType) {
      case 'Add Data Mapping':
        return 'Apply This';
      case 'Select Entity.attribute':
        return 'Apply This';
      case 'Condition Potentiality':
        return 'Apply This';
      default:
        return 'Save Configuration';
    }
  }

  bool _isFormValid() {
    switch (selectedModalType) {
      case 'Nested Function Configuration':
        return _nestedFunctionData.isValid();
      case 'Add Data Mapping':
        return _dataMappingData.isValid();
      case 'Select Entity.attribute':
        return _selectEntityAttributeData.isValid();
      case 'Condition Potentiality':
        return _conditionPotentialityData.isValid();
      default:
        return true;
    }
  }

  Widget _buildModalContent() {
    switch (selectedModalType) {
      case 'Nested Function Configuration':
        return _buildNestedFunctionContent();
      case 'Add Data Mapping':
        return _buildDataMappingContent();
      case 'Select Entity.attribute':
        return _buildSelectEntityContent();
      case 'Condition Potentiality':
        return _buildConditionPotentialityContent();
      default:
        return _buildNestedFunctionContent();
    }
  }

  Widget _buildNestedFunctionContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Attribute name section
        Text(
          widget.attributeName ?? 'Attribute name',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelLarge(context),
            fontWeight: FontWeight.w600,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 20),

        // Function Type and Function Name side by side
        Row(
          children: [
            // Function Type with info icon (left side)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Function Type',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Tooltip(
                        message:
                            'Select function operation (e.g., Create Record, Update Record, Fetch Records)',
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8F5E8),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                              color: const Color(0xFFB8E6B8), width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        textStyle: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black87,
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        margin: const EdgeInsets.all(2),
                        preferBelow: true,
                        verticalOffset: 8,
                        waitDuration: const Duration(milliseconds: 300),
                        showDuration: const Duration(seconds: 4),
                        child: Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Color(0xFF779FED),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select Function Type',
                    list: [
                      'Conditional Assignment',
                      'Send Email',
                      'Validate Data',
                      'Create Record',
                      'Update Record',
                      'Calculate'
                    ],
                    value: _nestedFunctionData.functionType.isEmpty
                        ? null
                        : _nestedFunctionData.functionType,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        _nestedFunctionData = _nestedFunctionData.copyWith(
                          functionType: selectedValue as String,
                        );
                        // Reset Calculate-specific state when function type changes
                        if (selectedValue != 'Calculate') {
                          _showCalculateDropdowns = false;
                          _showSourceSection = false;
                          _selectedEntityAttribute = '';
                          _expressionInput = '';
                        }
                      });

                      // Update the parent table with the new function type
                      if (widget.onFunctionTypeChanged != null) {
                        // Convert display value back to internal value
                        String internalValue = selectedValue as String;
                        switch (selectedValue as String) {
                          case 'Send Email':
                            internalValue = 'Send email';
                            break;
                          case 'Conditional Assignment':
                            internalValue = 'Conditional Assignment';
                            break;
                          case 'Validate Data':
                            internalValue = 'Validate Data';
                            break;
                          case 'Create Record':
                            internalValue = 'Create Record';
                            break;
                          case 'Update Record':
                            internalValue = 'Update Record';
                            break;
                          case 'Calculate':
                            internalValue = 'Calculate';
                            break;
                        }
                        widget.onFunctionTypeChanged!(internalValue);
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 24),
            // Function Name - just text display
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Function Name',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 13),
                  Text(
                    _nestedFunctionData.functionType == 'Calculate'
                        ? 'order id: calculate'
                        : '${widget.attributeName ?? 'Attribute name'}: ${_nestedFunctionData.functionType.isNotEmpty ? _nestedFunctionData.functionType : 'Function Type'}',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        if (_nestedFunctionData.functionType != 'Calculate') ...[
          // Function Inputs section
          Text(
            'Function Inputs',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          // Input Attributes label and dropdown in a row
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Input Attributes',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    CustomDropdownWidget(
                      label: _isLoadingAttributes
                          ? 'Loading Attributes...'
                          : 'Select Input Attributes',
                      list: _dynamicAttributeOptions.isNotEmpty
                          ? _dynamicAttributeOptions
                          : ['Loading...'],
                      value: _nestedFunctionData.functionInputs.isEmpty
                          ? null
                          : _nestedFunctionData.functionInputs,
                      onChanged: (dynamic value) {
                        if (!_isLoadingAttributes) {
                          setState(() {
                            _nestedFunctionData = _nestedFunctionData.copyWith(
                              functionInputs: value as String,
                            );
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              const Expanded(
                  flex: 1, child: SizedBox()), // Empty space on the right
            ],
          ),
          // Helper text
          Text(
            'Specify input entities and attributes (use * for required)',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelSmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
        ],

        // Additional Function Inputs for Calculate type
        if (_nestedFunctionData.functionType == 'Calculate') ...[
          Text(
            'Function Inputs',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          // Custom input with integrated dropdown
          Row(
            children: [
              Flexible(
                child: CustomInputWithDropdown(
                  hintText: '',
                  dropdownOptions: _dynamicAttributeOptions.isNotEmpty
                      ? _dynamicAttributeOptions
                      : ['Loading...'],
                  selectedDropdownValue: _selectedEntityAttribute,
                  onInputChanged: (value) {
                    setState(() {
                      _expressionInput = value;
                    });
                  },
                  onDropdownChanged: (value) {
                    setState(() {
                      _selectedEntityAttribute = value;
                      // Don't show dropdowns immediately - only after checkmark is clicked
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(Icons.close, size: 16),
                onPressed: () {
                  // Handle clear action
                  setState(() {
                    _showCalculateDropdowns = false;
                    _showSourceSection = false;
                    _selectedEntityAttribute = '';
                    _expressionInput = '';
                  });
                },
              ),
              IconButton(
                icon: Icon(Icons.check, size: 16, color: Colors.blue),
                onPressed: () {
                  // Handle confirm action - show dropdowns
                  setState(() {
                    _showCalculateDropdowns = true;
                    _showSourceSection = true;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Tooltip/hint text
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'Specify your expression. For Eg. Entity.Attribute is calculated using (entity1.attribute2 * entity 3.attribute1) + 5',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Select Entity.Attributes dropdown - only show after checkmark is clicked
          if (_showCalculateDropdowns) ...[
            Text(
              'Select Entity.Attributes',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelLarge(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            CustomDropdownWidget(
              label: 'Select Entity.Attribute',
              list: _dynamicAttributeOptions.isNotEmpty
                  ? _dynamicAttributeOptions
                  : ['Loading...'],
              value: _selectedEntityAttribute.isEmpty
                  ? 'Select Entity.Attribute'
                  : _selectedEntityAttribute,
              onChanged: (dynamic selectedValue) {
                setState(() {
                  _selectedEntityAttribute = selectedValue as String;
                });
              },
            ),
            const SizedBox(height: 16),
          ],

          // Source section
          Text(
            'Source:',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 8),

          // Source dropdown
          CustomDropdownWidget(
            label: 'Select Type',
            list: ['Mapping', 'Direct Value', 'Expression'],
            value: null,
            onChanged: (dynamic value) {
              // Handle source type selection
            },
          ),
          const SizedBox(height: 16),

          // Multiple source inputs
          if (_showSourceSection)
            Column(
              children: [
                // First source input
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Source: Order.ProductValue',
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: CustomDropdownWidget(
                                  label: 'Mapping',
                                  list: ['Mapping', 'Direct', 'Expression'],
                                  value: 'Mapping',
                                  onChanged: (dynamic value) {
                                    // Handle mapping selection
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: CustomDropdownWidget(
                                  label: 'Select LO',
                                  list: ['LO Names', 'Order LO', 'Product LO'],
                                  value: 'LO Names',
                                  onChanged: (dynamic value) {
                                    // Handle LO selection
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Second source input
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Source: Order.ProductQty',
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: CustomDropdownWidget(
                                  label: 'Mapping',
                                  list: ['Mapping', 'Direct', 'Expression'],
                                  value: 'Mapping',
                                  onChanged: (dynamic value) {
                                    // Handle mapping selection
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: CustomDropdownWidget(
                                  label: 'Select LO',
                                  list: ['LO Names', 'Order LO', 'Product LO'],
                                  value: 'LO Names',
                                  onChanged: (dynamic value) {
                                    // Handle LO selection
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          const SizedBox(height: 24),
        ],

        // Condition section
        if (_nestedFunctionData.functionType != 'Calculate') ...[
          Text(
            'Condition',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.labelLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),

          // Condition text input with 34px height
          Container(
            height: 34,
            child: TextField(
              controller: _textControllers['nestedFunction_condition']!,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(2),
                  borderSide:
                      const BorderSide(color: Color(0xFFCECECE), width: 1),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(2),
                  borderSide:
                      const BorderSide(color: Color(0xFFCECECE), width: 1),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(2),
                  borderSide:
                      const BorderSide(color: Color(0xFF0058FF), width: 1),
                ),
                contentPadding: const EdgeInsets.only(
                    left: 12, bottom: 12, top: 12, right: 12),
                filled: false,
                isDense: true,
                hintText: 'Enter condition',
                hintStyle: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleSmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[500],
                ),
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              onChanged: (value) => setState(() {}),
            ),
          ),
        ],
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildDataMappingContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 26),
        Text(
          'LO name - Entity - Attribute Name',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.bold,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 19),

        // Select Source LO 1 and Select Source Entity Attribute
        Row(
          children: [
            Expanded(
              child: _buildCustomDropdownField(
                'Select Source LO 1',
                _dataMappingData.sourceLO1.isEmpty
                    ? null
                    : _dataMappingData.sourceLO1,
                _getDynamicLOOptions(context),
                (value) {
                  setState(() {
                    _dataMappingData = _dataMappingData.copyWith(
                      sourceLO1: value,
                    );
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCustomDropdownField(
                'Select Source Entity.Attribute',
                _dataMappingData.sourceEntityAttribute1.isEmpty
                    ? null
                    : _dataMappingData.sourceEntityAttribute1,
                _getDynamicEntityAttributeOptions(context),
                (value) {
                  setState(() {
                    _dataMappingData = _dataMappingData.copyWith(
                      sourceEntityAttribute1: value,
                    );
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 30),

        // Source LO 2
        Text(
          'Source LO 2',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildCustomDropdownField(
                'Select Source LO 2',
                _dataMappingData.sourceLO2.isEmpty
                    ? null
                    : _dataMappingData.sourceLO2,
                _getDynamicLOOptions(context),
                (value) {
                  setState(() {
                    _dataMappingData = _dataMappingData.copyWith(
                      sourceLO2: value,
                    );
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildCustomDropdownField(
                'Entity.Attribute',
                _dataMappingData.sourceEntityAttribute2.isEmpty
                    ? null
                    : _dataMappingData.sourceEntityAttribute2,
                _getDynamicEntityAttributeOptions(context),
                (value) {
                  setState(() {
                    _dataMappingData = _dataMappingData.copyWith(
                      sourceEntityAttribute2: value,
                    );
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        Text(
          'Attribute LO is not option to map. Depends on your requirement',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.labelSmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Color(0xfffF0000),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildSelectEntityContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Source Entity.Attribute section
        Text(
          'Source Entity.Attribute',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Source Entity.Attribute dropdown with half width
        Row(
          children: [
            Expanded(
              flex: 1,
              child: CustomDropdownWidget(
                label: 'Select',
                list: [
                  'Select',
                  'Customer.ID',
                  'Customer.Name',
                  'Customer.Email',
                  'Customer.Phone',
                  'Address.Street',
                  'Address.City',
                  'Address.State',
                  'Address.ZipCode',
                  'Address.Country',
                  'Address11.Line1',
                  'Address11.Line2',
                  'Address11.City',
                  'Address11.State',
                  'Address11.ZipCode',
                  'Order.ID',
                  'Order.Date',
                  'Order.Amount',
                  'Product.ID',
                  'Product.Name',
                ],
                value: _selectEntityAttributeData.sourceEntityAttribute.isEmpty
                    ? 'Select'
                    : _selectEntityAttributeData.sourceEntityAttribute,
                onChanged: (dynamic selectedValue) {
                  setState(() {
                    _selectEntityAttributeData =
                        _selectEntityAttributeData.copyWith(
                      sourceEntityAttribute: selectedValue as String,
                    );
                  });
                },
              ),
            ),
            const Expanded(
              flex: 1,
              child: SizedBox(), // Empty space to make dropdown half width
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Condition section
        Text(
          'Condition',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),

        // Condition input field
        Container(
          height: 40,
          child: TextField(
            controller: _textControllers['selectEntity_condition']!,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide:
                    const BorderSide(color: Color(0xFF0058FF), width: 1),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              filled: true,
              fillColor: Colors.white,
              hintText: 'Enter condition',
              hintStyle: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[500],
              ),
            ),
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            onChanged: (value) => setState(() {}),
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildConditionPotentialityContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),

        // Two dropdown fields in a row
        Row(
          children: [
            // Select Source Entity
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Source Entity',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select',
                    list: [
                      'Select',
                      'Customer',
                      'Order',
                      'Product',
                      'Address',
                      'Payment',
                      'Invoice',
                      'User',
                      'Account',
                      'Transaction',
                    ],
                    value: _conditionPotentialityData.sourceEntity.isEmpty
                        ? 'Select'
                        : _conditionPotentialityData.sourceEntity,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        _conditionPotentialityData =
                            _conditionPotentialityData.copyWith(
                          sourceEntity: selectedValue as String,
                          sourceEntityAttribute:
                              '', // Reset the attribute dropdown when entity changes
                        );
                      });
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Source Entity.Attribute
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Source Entity.Attribute',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CustomDropdownWidget(
                    label: 'Select Entity.Attribute',
                    list: _getEntityAttributes(
                        _conditionPotentialityData.sourceEntity),
                    value:
                        _conditionPotentialityData.sourceEntityAttribute.isEmpty
                            ? 'Select Entity.Attribute'
                            : _conditionPotentialityData.sourceEntityAttribute,
                    onChanged: (dynamic selectedValue) {
                      setState(() {
                        _conditionPotentialityData =
                            _conditionPotentialityData.copyWith(
                          sourceEntityAttribute: selectedValue as String,
                        );
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 80), // Extra spacing to match the design
      ],
    );
  }

  /// Get attributes based on selected entity
  List<String> _getEntityAttributes(String entity) {
    switch (entity) {
      case 'Customer':
        return [
          'Select Entity.Attribute',
          'Customer.ID',
          'Customer.Name',
          'Customer.Email',
          'Customer.Phone',
          'Customer.Address',
          'Customer.DateOfBirth',
          'Customer.Status',
        ];
      case 'Order':
        return [
          'Select Entity.Attribute',
          'Order.ID',
          'Order.Date',
          'Order.Amount',
          'Order.Status',
          'Order.CustomerID',
          'Order.PaymentMethod',
        ];
      case 'Product':
        return [
          'Select Entity.Attribute',
          'Product.ID',
          'Product.Name',
          'Product.Price',
          'Product.Category',
          'Product.Stock',
          'Product.Description',
        ];
      case 'Address':
        return [
          'Select Entity.Attribute',
          'Address.Street',
          'Address.City',
          'Address.State',
          'Address.ZipCode',
          'Address.Country',
        ];
      case 'Payment':
        return [
          'Select Entity.Attribute',
          'Payment.ID',
          'Payment.Amount',
          'Payment.Method',
          'Payment.Status',
          'Payment.Date',
        ];
      case 'Invoice':
        return [
          'Select Entity.Attribute',
          'Invoice.ID',
          'Invoice.Number',
          'Invoice.Date',
          'Invoice.Amount',
          'Invoice.Status',
        ];
      case 'User':
        return [
          'Select Entity.Attribute',
          'User.ID',
          'User.Username',
          'User.Email',
          'User.Role',
          'User.LastLogin',
        ];
      case 'Account':
        return [
          'Select Entity.Attribute',
          'Account.ID',
          'Account.Number',
          'Account.Type',
          'Account.Balance',
          'Account.Status',
        ];
      case 'Transaction':
        return [
          'Select Entity.Attribute',
          'Transaction.ID',
          'Transaction.Amount',
          'Transaction.Type',
          'Transaction.Date',
          'Transaction.Status',
        ];
      default:
        return [
          'Select Entity.Attribute',
        ];
    }
  }

  /// Generate dynamic LO options for dropdown based on current LO index
  /// Shows only previous LOs according to business logic for DataMapping
  List<String> _getDynamicLOOptions(BuildContext context) {
    try {
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);

      // Get current LO index from widget parameter
      final currentLoIndex = widget.loIndex;

      if (currentLoIndex == null) {
        return ['No LOs available'];
      }

      final currentGoModel = goDetailsProvider.currentGoModel;
      if (currentGoModel?.localObjectivesList == null) {
        return ['No LOs available'];
      }

      // Get previous LOs only (LOs that come before the current LO)
      final previousLOOptions = <String>[];

      // Iterate through all LOs before the current LO
      for (int i = 0; i < currentLoIndex; i++) {
        if (i < currentGoModel!.localObjectivesList!.length) {
          final loName =
              currentGoModel.localObjectivesList![i].name ?? 'LO-${i + 1}';
          previousLOOptions.add(loName);
        }
      }

      return previousLOOptions.isNotEmpty
          ? previousLOOptions
          : ['No previous LOs available'];
    } catch (e) {
      // Fallback to hardcoded options if provider access fails
      return ['Cash', 'Credit', 'Debit'];
    }
  }

  /// Generate dynamic Entity.Attribute options from available LOs
  /// Shows only entities and attributes from previous LOs for DataMapping
  List<String> _getDynamicEntityAttributeOptions(BuildContext context) {
    Logger.info('🔍 _getDynamicEntityAttributeOptions called');

    try {
      final goDetailsProvider =
          Provider.of<GoDetailsProvider>(context, listen: false);
      Logger.info('🔍 GoDetailsProvider accessed successfully');

      // Get current LO index from widget parameter
      final currentLoIndex = widget.loIndex;
      Logger.info('🔍 Current LO index: $currentLoIndex');

      if (currentLoIndex == null) {
        Logger.info('❌ Current LO index is null');
        return ['Select Entity.Attribute'];
      }

      final currentGoModel = goDetailsProvider.currentGoModel;
      if (currentGoModel?.localObjectivesList == null) {
        Logger.info('❌ CurrentGoModel or localObjectivesList is null');
        return ['Select Entity.Attribute'];
      }

      Logger.info(
          '🔍 Total LOs available: ${currentGoModel!.localObjectivesList!.length}');

      final entityAttributeOptions = <String>[];

      // Get previous LO indices only (LOs that come before the current LO)
      final availableLoIndices = <int>[];

      // Add only previous LO indices
      for (int i = 0; i < currentLoIndex; i++) {
        availableLoIndices.add(i);
      }

      Logger.info('🔍 Previous LO indices: $availableLoIndices');

      // Extract entities and attributes from available LOs
      for (final loIndex in availableLoIndices) {
        if (loIndex < currentGoModel.localObjectivesList!.length) {
          final lo = currentGoModel.localObjectivesList![loIndex];
          Logger.info('🔍 Processing LO-${loIndex + 1}: ${lo.name}');

          // First check the LO's entitiesList property
          if (lo.entitiesList != null && lo.entitiesList!.isNotEmpty) {
            Logger.info(
                '🔍 LO-${loIndex + 1} has ${lo.entitiesList!.length} entities in entitiesList');

            for (int entityIndex = 0;
                entityIndex < lo.entitiesList!.length;
                entityIndex++) {
              final entity = lo.entitiesList![entityIndex];
              final entityName =
                  entity.displayName ?? entity.name ?? 'Unknown Entity';
              Logger.info('🔍   Entity $entityIndex: $entityName');

              if (entity.attributes != null && entity.attributes!.isNotEmpty) {
                Logger.info(
                    '🔍     Entity has ${entity.attributes!.length} attributes');

                for (int attrIndex = 0;
                    attrIndex < entity.attributes!.length;
                    attrIndex++) {
                  final attribute = entity.attributes![attrIndex];
                  final attributeName = attribute.displayName ??
                      attribute.name ??
                      'Unknown Attribute';
                  final entityAttributeOption = '$entityName.$attributeName';
                  Logger.info(
                      '🔍       Attribute $attrIndex: $attributeName -> $entityAttributeOption');

                  // Avoid duplicates
                  if (!entityAttributeOptions.contains(entityAttributeOption)) {
                    entityAttributeOptions.add(entityAttributeOption);
                    Logger.info('✅       Added: $entityAttributeOption');
                  } else {
                    Logger.info(
                        '⚠️       Duplicate skipped: $entityAttributeOption');
                  }
                }
              } else {
                Logger.info('❌     Entity has no attributes');
              }
            }
          } else {
            Logger.info('❌ LO-${loIndex + 1} has no entities in entitiesList');
          }

          // Also check the provider's LO-specific selected objects
          final loSelectedObjects =
              goDetailsProvider.getLoSelectedObjectsList(loIndex);
          if (loSelectedObjects.isNotEmpty) {
            Logger.info(
                '🔍 LO-${loIndex + 1} has ${loSelectedObjects.length} selected objects in provider');

            for (int objIndex = 0;
                objIndex < loSelectedObjects.length;
                objIndex++) {
              final selectedObjectData = loSelectedObjects[objIndex];
              final object = selectedObjectData.object;
              final attributes = selectedObjectData.attributes;

              final entityName =
                  object['displayName'] ?? object['name'] ?? 'Unknown Entity';
              Logger.info('🔍   Selected Object $objIndex: $entityName');

              if (attributes.isNotEmpty) {
                Logger.info(
                    '🔍     Object has ${attributes.length} attributes');

                for (int attrIndex = 0;
                    attrIndex < attributes.length;
                    attrIndex++) {
                  final attributeName = attributes[attrIndex];
                  final entityAttributeOption = '$entityName.$attributeName';
                  Logger.info(
                      '🔍       Attribute $attrIndex: $attributeName -> $entityAttributeOption');

                  // Avoid duplicates
                  if (!entityAttributeOptions.contains(entityAttributeOption)) {
                    entityAttributeOptions.add(entityAttributeOption);
                    Logger.info('✅       Added: $entityAttributeOption');
                  } else {
                    Logger.info(
                        '⚠️       Duplicate skipped: $entityAttributeOption');
                  }
                }
              } else {
                Logger.info('❌     Selected object has no attributes');
              }
            }
          } else {
            Logger.info(
                '❌ LO-${loIndex + 1} has no selected objects in provider');
          }
        }
      }

      Logger.info(
          '🔍 Final entity.attribute options count: ${entityAttributeOptions.length}');
      Logger.info('🔍 Final options: $entityAttributeOptions');

      // Return options with default placeholder if no entities found
      if (entityAttributeOptions.isEmpty) {
        Logger.info(
            '❌ No entity.attribute options found, adding sample data for testing');
        // Add some sample entity.attribute options for testing when no real data is available
        entityAttributeOptions.addAll([
          'Customer.ID',
          'Customer.Name',
          'Customer.Email',
          'Order.ID',
          'Order.Amount',
          'Order.Date',
          'Payment.ID',
          'Payment.Status',
          'Payment.Method'
        ]);
      }

      // Add placeholder at the beginning
      final finalOptions = [
        'Select Entity.Attribute',
        ...entityAttributeOptions
      ];
      Logger.info(
          '✅ Returning ${finalOptions.length} total options: $finalOptions');
      return finalOptions;
    } catch (e, stackTrace) {
      Logger.info('❌ Exception in _getDynamicEntityAttributeOptions: $e');
      Logger.info('❌ Stack trace: $stackTrace');
      // Fallback to hardcoded options if provider access fails
      return [
        'Select Entity.Attribute',
        'Customer.ID',
        'Customer.Name',
        'Order.Amount'
      ];
    }
  }

  Widget _buildCustomDropdownField(String label, String? value,
      List<String> options, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        CustomDropdownWidget(
          label: label,
          list: options,
          value: (value != null && options.contains(value)) ? value : null,
          onChanged: (dynamic selectedValue) =>
              onChanged(selectedValue as String),
        ),
      ],
    );
  }

  /// Create updated ObjectAttribute with current modal form data
  ObjectAttribute _createUpdatedAttributeWithModalData(
      ObjectAttribute baseAttribute) {
    return ObjectAttribute(
      name: baseAttribute.name,
      displayName: baseAttribute.displayName,
      dataType: baseAttribute.dataType,
      required: baseAttribute.required,
      unique: baseAttribute.unique,
      defaultType: baseAttribute.defaultType,
      defaultValue: baseAttribute.defaultValue,
      description: baseAttribute.description,
      helperText: baseAttribute.helperText,
      enumValues: baseAttribute.enumValues,
      validation: baseAttribute.validation,
      isPrimaryKey: baseAttribute.isPrimaryKey,
      isForeignKey: baseAttribute.isForeignKey,
      dataSource: baseAttribute.dataSource,
      functionType: baseAttribute.functionType,
      status: baseAttribute.status,
      uiControl: baseAttribute.uiControl,
      errorMessage: baseAttribute.errorMessage,
      // Add modal form data directly to ObjectAttribute fields
      nestedFunctionAttributeName: _nestedFunctionData.attributeName,
      nestedFunctionType: _nestedFunctionData.functionType,
      nestedFunctionName: _nestedFunctionData.functionName,
      nestedFunctionInputs: _nestedFunctionData.functionInputs,
      nestedFunctionCondition: _nestedFunctionData.condition,
      dataMappingLoName: _dataMappingData.loName,
      dataMappingSourceLO1: _dataMappingData.sourceLO1,
      dataMappingSourceLO2: _dataMappingData.sourceLO2,
      // Separated entity and attribute fields with conversion
      dataMappingSourceEntity1: _convertDisplayNameToSeparatedNames(
          _dataMappingData.sourceEntityAttribute1, context)['entity'],
      dataMappingSourceAttribute1: _convertDisplayNameToSeparatedNames(
          _dataMappingData.sourceEntityAttribute1, context)['attribute'],
      dataMappingSourceEntity2: _convertDisplayNameToSeparatedNames(
          _dataMappingData.sourceEntityAttribute2, context)['entity'],
      dataMappingSourceAttribute2: _convertDisplayNameToSeparatedNames(
          _dataMappingData.sourceEntityAttribute2, context)['attribute'],
      // Target context fields (populated from current context)
      dataMappingTargetLO: _dataMappingData.targetLO,
      dataMappingTargetEntity: _dataMappingData.targetEntity,
      dataMappingTargetAttribute: _dataMappingData.targetAttribute,
      // Legacy combined fields (for backward compatibility)
      dataMappingSourceEntityAttribute1: _convertDisplayNameToActualName(
          _dataMappingData.sourceEntityAttribute1, context),
      dataMappingSourceEntityAttribute2: _convertDisplayNameToActualName(
          _dataMappingData.sourceEntityAttribute2, context),
      selectEntitySourceEntityAttribute:
          _selectEntityAttributeData.sourceEntityAttribute,
      selectEntityCondition: _selectEntityAttributeData.condition,
      conditionPotentialitySourceEntity:
          _conditionPotentialityData.sourceEntity,
      conditionPotentialitySourceEntityAttribute:
          _conditionPotentialityData.sourceEntityAttribute,
    );
  }

  /// Load modal form data from ObjectAttribute in GoModel
  void _loadModalFormDataFromObjectAttribute() {
    try {
      Logger.info('🔄 Loading modal form data from ObjectAttribute...');

      // Get GoDetailsProvider and current GoModel
      final goDetailsProvider = Provider.of<GoDetailsProvider>(
        context,
        listen: false,
      );

      final goModel = goDetailsProvider.currentGoModel;
      if (goModel == null) {
        Logger.info('❌ GoModel is null, cannot load modal form data');
        return;
      }

      // Get the current LO index from widget
      final loIndex = widget.loIndex;
      if (loIndex == null ||
          goModel.localObjectivesList == null ||
          loIndex >= goModel.localObjectivesList!.length) {
        Logger.info('❌ Invalid LO index or localObjectivesList');
        return;
      }

      final lo = goModel.localObjectivesList![loIndex];
      if (lo.entitiesList == null || lo.entitiesList!.isEmpty) {
        Logger.info('❌ No entities found in LO');
        return;
      }

      // Find the specific entity and attribute being edited
      final targetEntity = _findTargetEntity(lo.entitiesList!);
      if (targetEntity == null) {
        Logger.info('❌ Target entity not found: ${widget.objectId}');
        return;
      }

      final targetAttributeIndex = _findTargetAttributeIndex(targetEntity);
      if (targetAttributeIndex == -1) {
        Logger.info(
            '❌ Target attribute not found at rowIndex: ${widget.rowIndex}');
        return;
      }

      final targetAttribute = targetEntity.attributes![targetAttributeIndex];

      // Load modal form data from the specific ObjectAttribute
      _loadFormDataFromAttribute(targetAttribute);
      Logger.info(
          '✅ Loaded modal form data from ObjectAttribute: ${targetAttribute.displayName} (Entity: ${targetEntity.displayName ?? targetEntity.name}, Row: ${widget.rowIndex})');
    } catch (e) {
      Logger.info('❌ Error loading modal form data: $e');
    }
  }

  /// Load form data from ObjectAttribute fields
  void _loadFormDataFromAttribute(ObjectAttribute attribute) {
    // Load NestedFunctionFormData
    if (attribute.nestedFunctionAttributeName != null ||
        attribute.nestedFunctionType != null ||
        attribute.nestedFunctionName != null ||
        attribute.nestedFunctionInputs != null ||
        attribute.nestedFunctionCondition != null) {
      _nestedFunctionData = NestedFunctionFormData(
        attributeName: attribute.nestedFunctionAttributeName ?? '',
        functionType: attribute.nestedFunctionType ?? '',
        functionName: attribute.nestedFunctionName ?? '',
        functionInputs: attribute.nestedFunctionInputs ?? '',
        condition: attribute.nestedFunctionCondition ?? '',
      );
    }

    // Load DataMappingFormData
    if (attribute.dataMappingLoName != null ||
        attribute.dataMappingSourceLO1 != null ||
        attribute.dataMappingSourceLO2 != null ||
        attribute.dataMappingSourceEntity1 != null ||
        attribute.dataMappingSourceAttribute1 != null ||
        attribute.dataMappingSourceEntity2 != null ||
        attribute.dataMappingSourceAttribute2 != null ||
        attribute.dataMappingTargetLO != null ||
        attribute.dataMappingTargetEntity != null ||
        attribute.dataMappingTargetAttribute != null ||
        attribute.dataMappingSourceEntityAttribute1 != null ||
        attribute.dataMappingSourceEntityAttribute2 != null) {
      _dataMappingData = DataMappingFormData(
        loName: attribute.dataMappingLoName ?? '',
        sourceLO1: attribute.dataMappingSourceLO1 ?? '',
        sourceLO2: attribute.dataMappingSourceLO2 ?? '',
        // Separated entity and attribute fields
        sourceEntity1: attribute.dataMappingSourceEntity1 ?? '',
        sourceAttribute1: attribute.dataMappingSourceAttribute1 ?? '',
        sourceEntity2: attribute.dataMappingSourceEntity2 ?? '',
        sourceAttribute2: attribute.dataMappingSourceAttribute2 ?? '',
        // Target context fields
        targetLO: attribute.dataMappingTargetLO ?? '',
        targetEntity: attribute.dataMappingTargetEntity ?? '',
        targetAttribute: attribute.dataMappingTargetAttribute ?? '',
        // Legacy combined fields
        sourceEntityAttribute1:
            attribute.dataMappingSourceEntityAttribute1 ?? '',
        sourceEntityAttribute2:
            attribute.dataMappingSourceEntityAttribute2 ?? '',
      );
    }

    // Load SelectEntityAttributeFormData
    if (attribute.selectEntitySourceEntityAttribute != null ||
        attribute.selectEntityCondition != null) {
      _selectEntityAttributeData = SelectEntityAttributeFormData(
        sourceEntityAttribute:
            attribute.selectEntitySourceEntityAttribute ?? '',
        condition: attribute.selectEntityCondition ?? '',
      );
    }

    // Load ConditionPotentialityFormData
    if (attribute.conditionPotentialitySourceEntity != null ||
        attribute.conditionPotentialitySourceEntityAttribute != null) {
      _conditionPotentialityData = ConditionPotentialityFormData(
        sourceEntity: attribute.conditionPotentialitySourceEntity ?? '',
        sourceEntityAttribute:
            attribute.conditionPotentialitySourceEntityAttribute ?? '',
      );
    }
  }

  /// Find the target entity based on objectId from modal context
  ObjectCreationModel? _findTargetEntity(List<ObjectCreationModel> entities) {
    if (widget.objectId == null) {
      Logger.info('⚠️ No objectId provided, using first entity as fallback');
      return entities.isNotEmpty ? entities.first : null;
    }

    // Try to find entity by ID first
    for (final entity in entities) {
      if (entity.id == widget.objectId) {
        Logger.info('✅ Found target entity by ID: ${entity.id}');
        return entity;
      }
    }

    // Try to find entity by name as fallback
    for (final entity in entities) {
      if (entity.name == widget.objectId) {
        Logger.info('✅ Found target entity by name: ${entity.name}');
        return entity;
      }
    }

    // Try to find entity by displayName as fallback
    for (final entity in entities) {
      if (entity.displayName == widget.objectId) {
        Logger.info(
            '✅ Found target entity by displayName: ${entity.displayName}');
        return entity;
      }
    }

    Logger.info('❌ Target entity not found for objectId: ${widget.objectId}');
    Logger.info(
        'Available entities: ${entities.map((e) => '${e.id ?? "null"}|${e.name ?? "null"}|${e.displayName ?? "null"}').join(', ')}');

    // Return first entity as fallback
    return entities.isNotEmpty ? entities.first : null;
  }

  /// Find the target attribute index based on rowIndex from modal context
  int _findTargetAttributeIndex(ObjectCreationModel entity) {
    if (entity.attributes == null || entity.attributes!.isEmpty) {
      Logger.info('❌ Entity has no attributes');
      return -1;
    }

    if (widget.rowIndex == null) {
      Logger.info('⚠️ No rowIndex provided, using first attribute as fallback');
      return 0;
    }

    // Validate rowIndex is within bounds
    if (widget.rowIndex! < 0 || widget.rowIndex! >= entity.attributes!.length) {
      Logger.info(
          '❌ rowIndex ${widget.rowIndex} is out of bounds (0-${entity.attributes!.length - 1})');
      Logger.info(
          'Available attributes: ${entity.attributes!.map((a) => a.displayName ?? a.name).join(', ')}');
      return -1;
    }

    Logger.info('✅ Found target attribute at index: ${widget.rowIndex}');
    return widget.rowIndex!;
  }
}

/// Custom widget that combines a text input with a dropdown inside it
class CustomInputWithDropdown extends StatefulWidget {
  final String hintText;
  final List<String> dropdownOptions;
  final Function(String) onInputChanged;
  final Function(String) onDropdownChanged;
  final String? selectedDropdownValue;

  const CustomInputWithDropdown({
    Key? key,
    required this.hintText,
    required this.dropdownOptions,
    required this.onInputChanged,
    required this.onDropdownChanged,
    this.selectedDropdownValue,
  }) : super(key: key);

  @override
  State<CustomInputWithDropdown> createState() =>
      _CustomInputWithDropdownState();
}

class _CustomInputWithDropdownState extends State<CustomInputWithDropdown> {
  final TextEditingController _textController = TextEditingController();
  final LayerLink _layerLink = LayerLink();
  final GlobalKey _key = GlobalKey();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;
  List<dynamic> _parts = [];
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _textController.addListener(() {
      // The parent widget seems to handle onInputChanged
    });
  }

  @override
  void dispose() {
    _removeOverlay();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  String _getPartsAsString() {
    return _parts.map((p) {
      if (p is String) {
        return p;
      } else if (p is Map && p['type'] == 'chip') {
        return p['value'];
      }
      return '';
    }).join(' ');
  }

  void _onDropdownSelected(String option) {
    setState(() {
      final currentText = _textController.text.trim();
      if (currentText.isNotEmpty) {
        _parts.add(currentText);
      }
      _parts.add({'type': 'chip', 'value': option});
      _textController.clear();
      widget.onInputChanged(_getPartsAsString());
    });
    widget.onDropdownChanged(option);
    _removeOverlay();
    FocusScope.of(context).requestFocus(_focusNode);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isDropdownOpen = false;
  }

  void _showOverlay() {
    if (_key.currentContext == null) {
      return;
    }

    final RenderBox renderBox =
        _key.currentContext!.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    final Offset offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          Positioned.fill(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                _removeOverlay();
              },
            ),
          ),
          Positioned(
            left: offset.dx,
            top: offset.dy + size.height,
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              offset: Offset(0, size.height + 5),
              child: _buildDropdownOptions(),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isDropdownOpen = true;
  }

  Widget _buildDropdownOptions() {
    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        constraints: BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(2),
          color: Colors.white,
        ),
        child: ListView(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: widget.dropdownOptions.map((option) {
            return InkWell(
              onTap: () {
                _onDropdownSelected(option);
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Checkbox
                    Container(
                      margin: const EdgeInsets.only(right: 10),
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: widget.selectedDropdownValue == option
                              ? const Color(0xFF007AFF)
                              : Colors.grey.shade400,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(4),
                        color: widget.selectedDropdownValue == option
                            ? const Color(0xFF007AFF)
                            : Colors.transparent,
                      ),
                      child: widget.selectedDropdownValue == option
                          ? const Icon(
                              Icons.check,
                              size: 16,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    // Option text
                    Expanded(
                      child: Text(
                        option,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          color: Colors.black,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        constraints: BoxConstraints(minHeight: 35),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey,
              width: 1.0,
            ),
          ),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).requestFocus(_focusNode);
                },
                child: Wrap(
                  spacing: 6.0,
                  runSpacing: 4.0,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    ..._parts.map<Widget>((part) {
                      if (part is String) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            part,
                            style: FontManager.getCustomStyle(
                              fontSize: FontManager.s10,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        );
                      } else if (part is Map && part['type'] == 'chip') {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            part['value'],
                            style: FontManager.getCustomStyle(
                              fontSize: FontManager.s10,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        );
                      }
                      return Container();
                    }).toList(),
                    IntrinsicWidth(
                      child: RawKeyboardListener(
                        focusNode: FocusNode(),
                        onKey: (RawKeyEvent event) {
                          if (event.logicalKey ==
                                  LogicalKeyboardKey.backspace &&
                              _textController.text.isEmpty &&
                              _parts.isNotEmpty) {
                            setState(() {
                              _parts.removeLast();
                              widget.onInputChanged(_getPartsAsString());
                            });
                          }
                        },
                        child: TextField(
                          controller: _textController,
                          focusNode: _focusNode,
                          onChanged: (text) {
                            widget.onInputChanged(
                                _getPartsAsString() + ' ' + text);
                          },
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                            hintText: _parts.isEmpty ? widget.hintText : '',
                            hintStyle: FontManager.getCustomStyle(
                              fontSize: FontManager.s10,
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[500],
                            ),
                          ),
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Dropdown button
            Container(
              key: _key,
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: InkWell(
                onTap: () {
                  if (_overlayEntry == null) {
                    _showOverlay();
                  } else {
                    _removeOverlay();
                  }
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.selectedDropdownValue != null &&
                          widget.selectedDropdownValue!.isNotEmpty)
                        Text(
                          widget.selectedDropdownValue!,
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s10,
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      const SizedBox(width: 2),
                      Icon(
                        _isDropdownOpen
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600,
                        size: 12,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
