class UIControllerTypeMapping {
  /// Main mapping of UI types to their corresponding UI controllers
  static const Map<String, List<String>> uiTypeToControllers = {
    // INPUT UI TYPES
    'string': [
      'text',
      'multiline',
      'email',
      'password',
      'mobile_no',
      'hyperlink',
      'encrypt_text',
      'typeahead',
      'redact',
      'path',
      'rich_text',
    ],
    'text': [
      'text',
      'multiline',
      'email',
      'password',
      'mobile_no',
      'hyperlink',
      'encrypt_text',
      'typeahead',
      'redact',
      'path',
      'rich_text',
    ],

    'integer': [
      'integer',
      'decimal',
      'float',
      'number',
      'number_input',
      'currency',
      'currency_input',
      'metric',
    ],
    'decimal': [
      'integer',
      'decimal',
      'float',
      'number',
      'number_input',
      'currency',
      'currency_input',
      'metric',
    ],

    'array': [
      'dropdown',
      'radio_button',
      'checkbox',
      'multiselect',
      'chip_single',
      'chip_multi',
      'tag',
    ],
    'multiValue': [
      'dropdown',
      'radio_button',
      'checkbox',
      'multiselect',
      'chip_single',
      'chip_multi',
      'tag',
    ],

    'slider_input': [
      'basic_slider',
      'input_slider',
      'range_slider',
      'step_slider',
      'vertical_slider',
      'rating',
    ],

    'datetime': [
      'date',
      'date_time',
      'date_only',
      'time',
      'time_only',
      'current_date',
      'current_date_only',
      'current_time_only',
      'current_date_time',
      'date_range',
      'year',
      'year_month',
      'timer',
      'scheduler',
      'cu_clock',
      'duration',
    ],
    'date': [
      'date',
      'date_time',
      'date_only',
      'time',
      'time_only',
      'current_date',
      'current_date_only',
      'current_time_only',
      'current_date_time',
      'date_range',
      'year',
      'year_month',
      'timer',
      'scheduler',
      'cu_clock',
      'duration',
    ],

    'boolean': [
      'boolean',
      'checkbox',
      'radio_button',
      'click_able',
    ],

    // MEDIA UI TYPES
    'media_input': [
      'file',
      'file_ibdr',
      'image',
      'image_avatar',
      'video',
      'video_recording',
      'audio_recording',
      'streaming_video',
      'capture_image',
      'signature',
    ],

    'audio_controls': [
      'audio_recording',
    ],

    'video_controls': [
      'video',
      'video_recording',
      'streaming_video',
    ],

    'image_controls': [
      'image',
      'image_avatar',
      'capture_image',
    ],

    'file_controls': [
      'file',
      'file_ibdr',
      'preview_doc',
    ],

    // LOCATION & SCANNING UI TYPES
    'location_input': [
      'geolocation',
      'location',
    ],

    'scanning_input': [
      'qr_scanner',
      'qrcode_decoder',
    ],

    // DISPLAY UI TYPES
    'display_only': [
      'label',
      'list_label',
      'label_with_image',
      'text_avatar',
      'preview_doc',
      'html',
      'metric',
      'progression',
      'auto_identifier',
    ],

    'interactive_display': [
      'click_able',
      'cta_button',
      'redirection',
      'hyperlink',
    ],

    // SPECIALIZED UI TYPES
    'form_controls': [
      'text',
      'email',
      'password',
      'number',
      'dropdown',
      'checkbox',
      'radio_button',
      'date',
      'file',
    ],

    'dashboard_widgets': [
      'metric',
      'progression',
      'label',
      'html',
      'image',
      'rating',
      'basic_slider',
    ],

    'advanced_input': [
      'rich_text',
      'html',
      'typeahead',
      'scheduler',
      'signature',
      'lmm',
    ],

    'mobile_specific': [
      'mobile_no',
      'capture_image',
      'video_recording',
      'audio_recording',
      'geolocation',
      'qr_scanner',
    ],

    'web_specific': [
      'html',
      'rich_text',
      'file',
      'hyperlink',
      'preview_doc',
    ],

    // CATEGORY-BASED UI TYPES
    'user_profile': [
      'text',
      'email',
      'mobile_no',
      'image_avatar',
      'date_only',
      'dropdown',
      'multiselect',
      'password',
    ],

    'financial_input': [
      'currency',
      'currency_input',
      'decimal',
      'number_input',
      'date',
      'dropdown',
    ],

    'content_creation': [
      'text',
      'multiline',
      'rich_text',
      'html',
      'image',
      'video',
      'file',
      'tag',
    ],

    'survey_form': [
      'text',
      'multiline',
      'radio_button',
      'checkbox',
      'rating',
      'basic_slider',
      'dropdown',
      'multiselect',
    ],

    'settings_config': [
      'boolean',
      'checkbox',
      'dropdown',
      'radio_button',
      'basic_slider',
      'number_input',
      'text',
    ],

    'time_tracking': [
      'date_time',
      'timer',
      'duration',
      'scheduler',
      'time_only',
      'date_only',
      'cu_clock',
    ],

    'e_commerce': [
      'currency_input',
      'rating',
      'image',
      'multiselect',
      'dropdown',
      'number_input',
      'text',
    ],

    'social_media': [
      'text',
      'multiline',
      'image',
      'video',
      'tag',
      'rating',
      'click_able',
    ],

    'data_visualization': [
      'metric',
      'progression',
      'label',
      'html',
      'basic_slider',
      'range_slider',
    ],

    // ACCESSIBILITY UI TYPES
    'accessibility_friendly': [
      'text',
      'dropdown',
      'radio_button',
      'checkbox',
      'label',
      'button',
      'date',
      'number',
    ],

    'voice_input': [
      'audio_recording',
      'text', // for voice-to-text
    ],

    'gesture_input': [
      'signature',
      'basic_slider',
      'click_able',
    ],
  };

  /// Get UI controllers for a specific UI type
  static List<String> getUIControllersForType(String uiType) {
    return uiTypeToControllers[uiType] ?? [];
  }

  /// Get all available UI types
  static List<String> getAllUITypes() {
    return uiTypeToControllers.keys.toList()..sort();
  }

  /// Check if a UI controller exists in a specific UI type
  static bool isControllerInType(String uiType, String controller) {
    return uiTypeToControllers[uiType]?.contains(controller) ?? false;
  }

  /// Get UI types that contain a specific controller
  static List<String> getUITypesForController(String controller) {
    List<String> types = [];
    uiTypeToControllers.forEach((type, controllers) {
      if (controllers.contains(controller)) {
        types.add(type);
      }
    });
    return types;
  }

  /// Get controllers common to multiple UI types
  static List<String> getCommonControllers(List<String> uiTypes) {
    if (uiTypes.isEmpty) return [];

    Set<String> commonControllers =
        uiTypeToControllers[uiTypes.first]?.toSet() ?? {};

    for (int i = 1; i < uiTypes.length; i++) {
      Set<String> currentControllers =
          uiTypeToControllers[uiTypes[i]]?.toSet() ?? {};
      commonControllers = commonControllers.intersection(currentControllers);
    }

    return commonControllers.toList()..sort();
  }

  /// Get all controllers from multiple UI types (union)
  static List<String> getAllControllersFromTypes(List<String> uiTypes) {
    Set<String> allControllers = {};

    for (String type in uiTypes) {
      allControllers.addAll(uiTypeToControllers[type] ?? []);
    }

    return allControllers.toList()..sort();
  }

  /// Get recommended controllers for a UI type (first 5)
  static List<String> getRecommendedControllers(String uiType,
      {int limit = 5}) {
    List<String> controllers = getUIControllersForType(uiType);
    return controllers.take(limit).toList();
  }

  /// Search UI types by controller name
  static Map<String, List<String>> searchUITypesByController(
      String searchTerm) {
    Map<String, List<String>> results = {};

    uiTypeToControllers.forEach((type, controllers) {
      List<String> matchingControllers = controllers
          .where((controller) =>
              controller.toLowerCase().contains(searchTerm.toLowerCase()))
          .toList();

      if (matchingControllers.isNotEmpty) {
        results[type] = matchingControllers;
      }
    });

    return results;
  }

  /// Get UI type categories
  static Map<String, List<String>> getUITypeCategories() {
    return {
      'Input Types': [
        'text_input',
        'number_input',
        'selection_input',
        'slider_input',
        'date_time_input',
        'boolean_input',
      ],
      'Media Types': [
        'media_input',
        'audio_controls',
        'video_controls',
        'image_controls',
        'file_controls',
      ],
      'Location & Scanning': [
        'location_input',
        'scanning_input',
      ],
      'Display Types': [
        'display_only',
        'interactive_display',
      ],
      'Specialized Types': [
        'form_controls',
        'dashboard_widgets',
        'advanced_input',
        'mobile_specific',
        'web_specific',
      ],
      'Use Case Types': [
        'user_profile',
        'financial_input',
        'content_creation',
        'survey_form',
        'settings_config',
        'time_tracking',
        'e_commerce',
        'social_media',
        'data_visualization',
      ],
      'Accessibility Types': [
        'accessibility_friendly',
        'voice_input',
        'gesture_input',
      ],
    };
  }

  /// Get filtered controllers based on platform
  static List<String> getControllersForPlatform(
      String uiType, String platform) {
    List<String> allControllers = getUIControllersForType(uiType);

    switch (platform.toLowerCase()) {
      case 'mobile':
        return allControllers
            .where((controller) =>
                !['html', 'rich_text', 'preview_doc'].contains(controller))
            .toList();

      case 'web':
        return allControllers
            .where((controller) => ![
                  'capture_image',
                  'video_recording',
                  'audio_recording',
                  'qr_scanner'
                ].contains(controller))
            .toList();

      case 'desktop':
        return allControllers
            .where((controller) => ![
                  'geolocation',
                  'capture_image',
                  'qr_scanner'
                ].contains(controller))
            .toList();

      default:
        return allControllers;
    }
  }
}

// Usage Examples and Testing
