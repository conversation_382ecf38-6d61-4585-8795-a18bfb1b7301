import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:nsl/models/RoleCreatModel.dart';
import 'package:nsl/models/add_role_model.dart';
import 'package:nsl/models/parse_validation_entity/parse_validation_entity_model.dart';
import 'package:nsl/models/roles/det_departments_list.dart';
import 'package:nsl/models/roles/get_roles_list.dart';

import 'package:nsl/models/roles/inheritance_role_model.dart';
import 'package:nsl/models/roles/publish_entity_success.dart';
import 'package:nsl/models/roles/save_valid_dept_model.dart';
import 'package:nsl/models/roles/validate_department_model.dart';
import 'package:nsl/models/save_role_model.dart';

import 'package:nsl/models/parse_validation_entity/publish_entity_model.dart';
import 'package:nsl/screens/web/static_flow/nsl_entity_parser.dart';
import 'package:nsl/services/auth_service.dart';
import 'package:nsl/services/entity_parse_validation_service.dart';
import '../models/object_creation_model.dart';
import '../models/solution_creation_model.dart';
import '../services/object_creation_service.dart';
import '../utils/logger.dart';
import 'base_provider.dart';

class ObjectCreationProvider extends BaseProvider {
  final ObjectCreationService _objectCreationService = ObjectCreationService();
  final EntityParseValidationService _entityParseValidationService =
      EntityParseValidationService();

  // Data storage
  List<ObjectCreationModel> _objects = [];
  List<SolutionCreationModel> _solutions = [];
  ObjectCreationModel? _currentObject;
  SolutionCreationModel? _currentSolution;
  String? _currentSessionId; // Session-based API
  String? _currentUserIntent;
  AuthService _authService = AuthService();

  ParseValidationEntityModel? parseValidationEntityModel;

  ValidateAddRoleModel? validateRoleModel;
  SaveRoleModel? saveRoleModel;
  ValidateInheritanceModel? validateInheritanceModel;
  ValidateDepartmentModel? validateDepartmentModel;
  SaveValidDepartmentModel? saveValidDepartmentModel;

  PublishEntityModel? publishEntityModel;
  GetDepartmentsListModel? getDepartmentsListModel;
  GetRolesListModel? _getRolesListModel;
  PublishEntitySuccessModel? _publishEntitySuccessModel;

  // Specific loading states for different operations
  bool _isLoadingExtraction = false;
  bool _isLoadingJobEntities = false;
  bool _isUpdatingEntity = false;
  bool _isDeletingEntity = false;
  bool isEntityValidated = false;

  bool? showError = false;

  String? roleNameError;

  // Getters
  List<ObjectCreationModel> get objects => _objects;

  List<SolutionCreationModel> get solutions => _solutions;

  ObjectCreationModel? get currentObject => _currentObject;

  SolutionCreationModel? get currentSolution => _currentSolution;

  String? get currentSessionId => _currentSessionId;

  String? get currentUserIntent => _currentUserIntent;

  int get objectCount => _objects.length;

  bool get hasObjects => _objects.isNotEmpty;

  // Specific loading state getters
  bool get isLoadingExtraction => _isLoadingExtraction;

  bool get isLoadingJobEntities => _isLoadingJobEntities;

  bool get isUpdatingEntity => _isUpdatingEntity;

  bool get isDeletingEntity => _isDeletingEntity;
  GetRolesListModel? get getRolesListModel => _getRolesListModel;
  PublishEntitySuccessModel? get publishEntitySuccessModel =>
      _publishEntitySuccessModel;

  // Methods to set specific loading states
  void setLoadingExtraction(bool value) {
    if (_isLoadingExtraction != value) {
      _isLoadingExtraction = value;
      notifyListeners();
    }
  }

  void setLoadingJobEntities(bool value) {
    if (_isLoadingJobEntities != value) {
      _isLoadingJobEntities = value;
      notifyListeners();
    }
  }

  void setUpdatingEntity(bool value) {
    if (_isUpdatingEntity != value) {
      _isUpdatingEntity = value;
      notifyListeners();
    }
  }

  void setDeletingEntity(bool value) {
    if (_isDeletingEntity != value) {
      _isDeletingEntity = value;
      notifyListeners();
    }
  }

  // Constructor
  ObjectCreationProvider() {
    Logger.info('ObjectCreationProvider initialized');
  }

  /// Execute complete extraction workflow with user intent using new API
  Future<bool> executeCompleteExtractionWorkflow({
    required String userIntent,
    String tenantName = 'Organization',
    String businessDomain = 'General Business',
    String context = '',
  }) async {
    setLoadingExtraction(true);
    clearError();

    // Execute the session-based workflow with correct API endpoints
    final result = await runWithLoadingAndErrorHandling<ObjectCreationResponse>(
      () => _objectCreationService.executeCompleteExtractionWorkflowNew(
        userIntent: userIntent,
        tenantName: tenantName,
        businessDomain: businessDomain,
        context: context,
      ),
      context: 'ObjectCreationProvider.executeCompleteExtractionWorkflow',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success && result.data != null) {
      _objects = result.data!;
      _currentUserIntent = userIntent;
      _currentSessionId = result.sessionId; // Store session ID from new API
      Logger.info(
          'Successfully extracted ${_objects.length} objects with session: $_currentSessionId');
      setLoadingExtraction(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to extract entities';
      setError(errorMessage);
      Logger.error('Entity extraction failed: $errorMessage');
    } else {
      setError('Failed to extract entities. Please try again.');
      Logger.error('Entity extraction failed: No result returned');
    }

    setLoadingExtraction(false);
    return false;
  }

  /// Get entities for a specific session using new API
  Future<bool> getSessionEntities(String sessionId) async {
    setLoadingJobEntities(true);
    clearError();

    final result =
        await runWithLoadingAndErrorHandling<ObjectCreationResponse?>(
      () => _objectCreationService.getSessionEntities(sessionId),
      context: 'ObjectCreationProvider.getSessionEntities',
      showLoading: false, // We're managing loading state manually
    );

    if (result != null && result.success && result.data != null) {
      _objects = result.data!;
      _currentSessionId = sessionId;
      Logger.info(
          'Successfully fetched ${_objects.length} entities for session: $sessionId');
      setLoadingJobEntities(false);
      return true;
    } else if (result != null) {
      final errorMessage = result.message ?? 'Failed to fetch session entities';
      setError(errorMessage);
      Logger.error('Failed to fetch session entities: $errorMessage');
    } else {
      setError('Failed to fetch session entities. Please try again.');
      Logger.error('Failed to fetch session entities: No result returned');
    }

    setLoadingJobEntities(false);
    return false;
  }

  /// Set current object for detailed view
  void setCurrentObject(ObjectCreationModel? object) {
    _currentObject = object;
    notifyListeners();
  }

  /// Clear all data
  void clearData() {
    _objects.clear();
    _currentObject = null;
    _currentSessionId = null;
    _currentUserIntent = null;
    clearError();
    notifyListeners();
    Logger.info('ObjectCreationProvider data cleared');
  }

  /// Refresh data based on current context
  Future<bool> refreshData() async {
    if (_currentSessionId != null) {
      return await getSessionEntities(_currentSessionId!);
    } else if (_currentUserIntent != null) {
      return await executeCompleteExtractionWorkflow(
        userIntent: _currentUserIntent!,
      );
    }
    return false;
  }

  Future<bool> parseValidateEntity(
    ObjectCreationModel model,
    ObjectCreationModel? currentData, {
    bool isEditMode = false,
  }) async {
    try {
      final savedData = await _authService.getSavedAuthData();
      final tenantName = savedData.data?.user?.tenantName ?? '';
      final naturalLanguage =
          EntityTextFormatter.getEntitySection(model, tenantName);

      final result = await _entityParseValidationService.parseValidateEntity(
        nautralLanguage: naturalLanguage,
      );

      parseValidationEntityModel = result;

      if (parseValidationEntityModel?.success ?? false) {
        final result1 =
            await _entityParseValidationService.parseValidateSaveEntity(
                nautralLanguage: naturalLanguage, isEditMode: isEditMode);
        parseValidationEntityModel = result1;
        if (parseValidationEntityModel?.success ?? false) {
          currentData?.id = parseValidationEntityModel?.savedData?.entityId;
          currentData?.isEntityValidatedSaved = true;
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    } catch (e, s) {
      if (kDebugMode) {
        print("$e. $s");
      }
      return true;
    }
  }

  Future<bool> parseValidateEntityAttributes(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final savedData = await _authService.getSavedAuthData();
    final tenantName = savedData.data?.user?.tenantName ?? '';
    final naturalLanguage =
        EntityTextFormatter.getAttributesSection(model, tenantName);

    final result = await _entityParseValidationService
        .parseValidateEntityAttributes(nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntityAttributes(nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntityAttributesValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateEntityRelationships(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final savedData = await _authService.getSavedAuthData();
    final tenantName = savedData.data?.user?.tenantName ?? '';
    final naturalLanguage =
        EntityTextFormatter.getRelationshipsSection(model, tenantName);

    final result = await _entityParseValidationService
        .parseValidateEntityRelationships(nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntityRelationships(
              nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntityRelationshipsValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateEntityAttributeValidations(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final savedData = await _authService.getSavedAuthData();
    final tenantName = savedData.data?.user?.tenantName ?? '';
    final naturalLanguage =
        EntityTextFormatter.getBusinessRulesSection(model, tenantName);

    final result = await _entityParseValidationService
        .parseValidateEntityAttributeValidation(
            nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntityAttributeValidations(
              nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntityBusinessRulesValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateEntityEnumValues(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final savedData = await _authService.getSavedAuthData();
    final tenantName = savedData.data?.user?.tenantName ?? '';
    final naturalLanguage =
        EntityTextFormatter.getEnumsSection(model, tenantName);

    final result = await _entityParseValidationService
        .parseValidateEntityEnumValues(nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntityEnumValues(nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntityEnumValuesValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateEntitySystemPermissions(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final savedData = await _authService.getSavedAuthData();
    final tenantName = savedData.data?.user?.tenantName ?? '';
    final naturalLanguage =
        EntityTextFormatter.getSystemPermissionsSection(model, tenantName);

    final result = await _entityParseValidationService
        .parseValidateEntitySystemPermissions(nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntitySystemPermissions(
              nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntitySystemPropertiesValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateEntitySecurityProperties(
    ObjectCreationModel model,
    ObjectCreationModel? currentData,
  ) async {
    final naturalLanguage = EntityTextFormatter.getSecuritySection(model);

    final result = await _entityParseValidationService
        .parseValidateEntitySecurityProperties(
            nautralLanguage: naturalLanguage);

    parseValidationEntityModel = result;

    if (parseValidationEntityModel?.success ?? false) {
      final result1 = await _entityParseValidationService
          .parseValidateSaveEntitySecurityProperties(
              nautralLanguage: naturalLanguage);
      parseValidationEntityModel = result1;
      if (parseValidationEntityModel?.success ?? false) {
        currentData?.isEntitySecurityClassificationValidatedSaved = true;
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> parseValidateAddRoleEntity(List<RoleCreateModel> model,
      {bool isEditMode = false}) async {
    print(
        '=== DEBUGGING: ObjectCreationProvider.parseValidateAddRoleEntity called with isEditMode: $isEditMode ===');
    final roleValidate = EntityTextFormatter.getAddRoleEntitySection(model);
    print('Role validation text: $roleValidate');

    final result = await runWithLoadingAndErrorHandling<ValidateAddRoleModel?>(
      () => _entityParseValidationService.parseValidateaddRoleEntity(
        nautralLanguage: roleValidate,
      ),
      context: 'ObjectCreationProvider.parseValidationEntity',
      showLoading: false,
    );

    validateRoleModel = result;

    if (validateRoleModel?.hasErrors == false) {
      final result = await runWithLoadingAndErrorHandling<SaveRoleModel?>(
        () => _entityParseValidationService.saveValidatedRoleEntity(
            nautralLanguage: roleValidate, isEditMode: isEditMode),
        context: 'ObjectCreationProvider.parseValidationEntity',
        showLoading: false,
      );

      saveRoleModel = result;

      isEntityValidated = true;
      return false;
    } else {
      return true;
    }
  }

  Future<bool> parseValidateDeptEntity(List<DeptCreateModel> model) async {
    final roleValidate = EntityTextFormatter.getAddDeptEntitySection(model);

    final result =
        await runWithLoadingAndErrorHandling<ValidateDepartmentModel?>(
      () => _entityParseValidationService.parseValidateDeptEntity(
          nautralLanguage: roleValidate),
      context: 'ObjectCreationProvider.parseValidationEntity',
      showLoading: false,
    );

    validateDepartmentModel = result;

    if (validateDepartmentModel?.isValid ?? false) {
      final result =
          await runWithLoadingAndErrorHandling<SaveValidDepartmentModel?>(
        () => _entityParseValidationService.saveValidatedDeptEntity(
            nautralLanguage: roleValidate),
        context: 'ObjectCreationProvider.parseValidationEntity',
        showLoading: false,
      );

      saveValidDepartmentModel = result;

      isEntityValidated = true;
      return false;
    } else {
      return true;
    }
  }

  Future<bool> parseValidateInheritanceEntity(
      List<InheritsCreateModel> model) async {
    final roleValidate = EntityTextFormatter.getInheritanceSection(model);

    final result =
        await runWithLoadingAndErrorHandling<ValidateInheritanceModel?>(
      () => _entityParseValidationService.parseValidateInheritanceEntity(
          nautralLanguage: roleValidate),
      context: 'ObjectCreationProvider.parseValidationEntity',
      showLoading: false,
    );

    validateInheritanceModel = result;
    log(jsonEncode(validateInheritanceModel));
    log(validateInheritanceModel!
        .inheritanceResults![0].validationResult!.structureErrors!.isNotEmpty
        .toString());

    if (validateInheritanceModel?.hasErrors ?? false) {
      log("============================================12");
      final result = await runWithLoadingAndErrorHandling<dynamic?>(
        () => _entityParseValidationService.parseSaveInheritanceEntity(
            nautralLanguage: roleValidate),
        context: 'ObjectCreationProvider.parseValidationEntity',
        showLoading: false,
      );

      // saveRoleModel=result;

      isEntityValidated = true;
      return false;
    } else {
      return true;
    }
  }

  Future<GetDepartmentsListModel?> fetchDepartment(String requiredTable) async {
    final result = await _entityParseValidationService.fetchDepartments(
        requiredTable: requiredTable);

    getDepartmentsListModel = result;

    if (getDepartmentsListModel?.success == true) {
      return getDepartmentsListModel;
    } else {
      return null;
    }
  }

  Future<GetRolesListModel?> fetchRoles(String requiredTable) async {
    final result = await _entityParseValidationService.fetchRoles(
        requiredTable: requiredTable);

    _getRolesListModel = result;

    if (_getRolesListModel?.success == true) {
      return _getRolesListModel;
    } else {
      return null;
    }
  }

  Future<bool> publish(ObjectCreationModel? object) async {
    final result =
        await _entityParseValidationService.publish(entityId: object?.id);

    publishEntityModel = result;

    if (publishEntityModel?.status?.toLowerCase() == "success") {
      return true;
    } else {
      return false;
    }
  }

  Future<PublishEntitySuccessModel?> publishRole(String? roleId,
      {isEditMode = false}) async {
    final result = await _entityParseValidationService.publishRole(
        entityId: roleId, isEditMode: isEditMode);

    _publishEntitySuccessModel = result;

    if (publishEntityModel?.status?.toLowerCase() == "success") {
      return _publishEntitySuccessModel;
    } else {
      return null;
    }
  }
}
